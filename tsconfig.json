{"compilerOptions": {"target": "esnext", "lib": ["dom", "dom.iterable", "esnext", "ESNext.Array"], "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "noEmit": true, "strict": true, "skipLibCheck": true, "noFallthroughCasesInSwitch": true, "moduleDetection": "force", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx", "types": ["vite/client", "vitest/globals"]}, "include": ["src", "vite.config.ts", "lint-staged.config.ts"]}