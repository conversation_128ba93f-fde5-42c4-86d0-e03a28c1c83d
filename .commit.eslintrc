{
  "extends": ["./.eslintrc"],
  "rules": {
    "object-shorthand": "error",
    "prefer-template": "error",
    "no-empty": ["error", { "allowEmptyCatch": true }],
    "unused-imports/no-unused-imports": "error",
    "unused-imports/no-unused-vars": [
      "error",
      {
        "vars": "all",
        "varsIgnorePattern": "^_",
        "args": "all",
        "argsIgnorePattern": "^_",
        "ignoreRestSiblings": true
      }
    ],
    "import/order": [
      0,
      {
        "groups": [
          "unknown",
          ["external", "builtin", "internal"],
          "parent",
          "index",
          "sibling"
        ],
        "newlines-between": "never",
        "alphabetize": { "order": "asc", "caseInsensitive": true }
      }
    ],
    "@typescript-eslint/no-unsafe-argument": 0 // Luke: later enable
  }
}
