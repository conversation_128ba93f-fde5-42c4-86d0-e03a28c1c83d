{"name": "note-auto-parser", "version": "0.1.0", "dependencies": {"@azure/msal-browser": "^2.27.0", "@fortawesome/fontawesome-free": "^5.15.3", "@microsoft/microsoft-graph-client": "^3.0.5", "@vitejs/plugin-react": "^4.2.1", "axios": "^1.6.8", "buffer": "^6.0.3", "date-fns": "^2.22.1", "fflate": "^0.8.1", "node-html-markdown": "^1.3.0", "node-html-parser": "^3.3.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^11.3.4", "react-hook-form": "^7.8.3", "react-router-dom": "^6.10.0", "sanitize-filename": "^1.6.3", "uuid": "^9.0.1", "vite": "^5.2.9", "vite-tsconfig-paths": "^4.3.2"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^11.2.6", "@testing-library/user-event": "^13.1.5", "@types/bun": "^1.1.0", "@types/jest": "^29.5.11", "@types/microsoft-graph": "^2.26.0", "@types/node": "^20.12.7", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^9.0.4", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "autoprefixer": "^10.4.19", "caniuse-lite": "^1.0.30001690", "eslint-plugin-import": "^2.29.1", "eslint-plugin-react": "^7.34.1", "eslint-plugin-unused-imports": "^3.1.0", "git-branch-is": "^4.0.0", "husky": "^9.0.11", "jest": "^29.7.0", "lint-staged": "^15.2.2", "postcss": "^8.4.38", "prettier": "^3.2.5", "tailwindcss": "^3.4.4", "ts-node": "^10.9.2", "tsc": "^2.0.4", "typescript": "^5.7.2", "vitest": "^1.5.0"}, "type": "module", "overrides": {"rollup": "npm:@rollup/wasm-node"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"transformIgnorePatterns": []}, "prettier": {"arrowParens": "avoid", "trailingComma": "all"}, "private": true, "scripts": {"start": "concurrently \"vite\" \"wait-on http://localhost:3000 && open http://localhost:3000\"", "dev": "yarn start", "build": "tsc && vite build", "serve": "vite preview", "typecheck": "node_modules/typescript/bin/tsc --project tsconfig.json", "lint-ci": "eslint -c .commit.eslintrc --ext .ts,.tsx --fix", "lint": "eslint -c .commit.eslintrc --fix src/**/*.ts src/**/*.tsx", "prepare": "husky"}, "optionalDependencies": {"@rollup/rollup-darwin-x64": "^4.14.3"}}