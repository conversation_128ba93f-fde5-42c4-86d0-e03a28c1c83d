{
  "root": true,
  "parser": "@typescript-eslint/parser",
  "parserOptions": { "project": ["./tsconfig.json"] },
  "plugins": ["unused-imports", "import", "react"],
  "env": { "es6": true, "jest": true, "browser": true },
  "extends": ["plugin:@typescript-eslint/recommended"],
  "rules": {
    "@typescript-eslint/await-thenable": "error",
    "@typescript-eslint/no-floating-promises": [
      "error",
      { "ignoreVoid": true }
    ],
    "@typescript-eslint/promise-function-async": "off",
    "@typescript-eslint/no-unused-expressions": [
      "error",
      { "allowShortCircuit": true }
    ],
    "import/no-default-export": "error",
    "@typescript-eslint/no-shadow": [
      "warn",
      {
        "ignoreTypeValueShadow": true,
        "ignoreFunctionTypeParameterNameValueShadow": true
      }
    ],
    "no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars": "off",
    "unused-imports/no-unused-imports": "off",
    "unused-imports/no-unused-vars": "off",
    "no-empty": "off",
    "@typescript-eslint/require-await": "off",
    "@typescript-eslint/explicit-module-boundary-types": "off",
    "@typescript-eslint/no-non-null-assertion": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/no-use-before-define": [
      "error",
      { "functions": false, "classes": false, "enums": false }
    ],
    "@typescript-eslint/no-empty-interface": [
      "error",
      { "allowSingleExtends": true }
    ],
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/strict-boolean-expressions": [
      "error",
      {
        "allowString": false,
        "allowNumber": false,
        "allowNullableObject": true
      }
    ],
    "no-var": "error",

    "@typescript-eslint/no-unsafe-argument": 0, // Luke: long-term would like to enable.  New dev task?
    "@typescript-eslint/no-unsafe-assignment": "off",
    "@typescript-eslint/no-unsafe-return": "off",
    "@typescript-eslint/no-unsafe-call": "off",
    "@typescript-eslint/no-unsafe-member-access": "off",
    "@typescript-eslint/restrict-template-expressions": "off",
    "@typescript-eslint/unbound-method": "off",
    "@typescript-eslint/no-misused-promises": "off",
    "react/jsx-uses-react": 1,
    "react/jsx-uses-vars": 1,
    "indent": "off",
    "eol-last": "off",
    "comma-dangle": "off",
    "operator-linebreak": "off",
    "space-before-function-paren": "off"
  }
}
