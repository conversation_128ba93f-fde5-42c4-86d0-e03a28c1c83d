import { navigateToTopNote } from "./<PERSON><PERSON><PERSON>/commandLineTools";
import { J_CURRENT_FOLDER_ID } from "./Jo<PERSON><PERSON>/Constants";
import { copySelectedNotesToFolder } from "./<PERSON><PERSON><PERSON>/Jo<PERSON>linShared";

async function runCopyNotesToCurrent() {
  const createdNotes = await copySelectedNotesToFolder(J_CURRENT_FOLDER_ID);

  navigateToTopNote(createdNotes);
}

await runCopyNotesToCurrent();
