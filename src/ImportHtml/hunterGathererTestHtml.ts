export const hunterGathererTestHtml = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "XHTML1-s.dtd">
<html xmlns="http://www.w3.org/TR/1999/REC-html-in-xml" xml:lang="en" lang="en">

<head>
  <meta charset="UTF-8">
</head>

<body>
  <div class="bodyContainer">
    <div class="notebookFor">
      Exportación de bloc de notas
    </div>
    <div class="bookTitle">
      A Hunter-Gatherer's Guide to the 21st Century: Evolution and the
      Challenges of Modern Life (English Edition)
    </div>
    <div class="authors">
      Hey<PERSON>, <PERSON>; <PERSON>, <PERSON>ret
    </div>
    <div class="citation">

    </div>

    <hr />

    <div class="sectionHeading">
      Introduction
    </div>

    <div class="noteHeading">
      Subrayar(<span class="highlight_yellow">amarillo</span>) - Página xiii ·
      Posición 135
    </div>
    <div class="noteText">
      Science is a method that oscillates between induction and deduction—
    </div>
    <div class="noteHeading">
      Nota - Página xiii · Posición 135
    </div>
    <div class="noteText">
      .z.b define this
    </div>

    <div class="noteHeading">
      Subrayar(<span class="highlight_yellow">amarillo</span>) - Página xiii ·
      Posición 136
    </div>
    <div class="noteText">
      We thus generate models of the world that, when we do the scientific work
      correctly, achieve three things: they predict more than what came before,
      assume less, and come to fit with one another, merging into a seamless
      whole.
    </div>
    <div class="noteHeading">
      Nota - Página xiii · Posición 138
    </div>
    <div class="noteText">
      .z.science .. Good framing.
    </div>
  </div>
</body>`;
