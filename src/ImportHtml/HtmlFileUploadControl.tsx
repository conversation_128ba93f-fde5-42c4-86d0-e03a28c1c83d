import { useCallback } from "react";
import * as parser from "node-html-parser";
import { useDropzone } from "react-dropzone";
import {
  getIdsFromTags,
  TaggedInput,
  getMdPatchForTaggedInput,
  MdPatch,
} from "../Components/getPatchForTaggedInput";
import { NoteSource, savePatches } from "../Components/savePatch";
import {
  Assert,
  dedup,
  getSourceFooter,
  hasValue,
  htmlFromContent,
  hv,
  insertBrInsideLastP,
  mdFromContent,
  no,
  P,
} from "../Tools";
import { NoteInput } from "../NoteInput";
import { getCustomDateFromText, getTaggedInputs } from "../getTags";
import { config } from "../Config";
import { TAG_ID_PAIRINGS } from "../PairingHelpers";

export enum NoteTextType {
  NoneSpecified = 0,
  WrittenNote = 1,
  Highlight = 2,
}

export function HtmlFileUploadControl() {
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const fileStr: string = await readFullFile(acceptedFiles[0]);
    const nodeList = getNodeListFromHtml(fileStr);
    const shouldIgnoreSections = shouldIgnoreSectionsInNodeList(nodeList);
    const sectionNoteInputs = getNoteInputsFromNodeList(
      nodeList,
      shouldIgnoreSections,
    );

    const sectionResults = sectionNoteInputs.map(inputs =>
      getPatchesForSection(inputs, shouldIgnoreSections),
    );

    const hasErrors = sectionResults.some(r => hv(r.errorResult));
    const invalidTags = sectionResults.flatMap(
      r => r.errorResult?.invalidTags ?? [],
    );

    if (hasErrors) {
      if (invalidTags.length > 0) {
        console.log(`*** Not running, invalid tags found:`);
        console.log(dedup(invalidTags).toSorted());
      } else {
        console.log("Skipping patching - untagged content");
      }

      return;
    }
    const patches = sectionResults.flatMap(r => r.patches!);

    let customDate: Date | null = null;

    while (customDate === null) {
      // HTML files never have date so prompt for a reasonable one now that
      // we've confirmed all patches
      const customDateStr = prompt("Date string? M-d, yyyy-M-d") ?? no();
      customDate = getCustomDateFromText(`((${customDateStr.trim()}))`);
      console.log(customDate);
    }

    await savePatches(
      patches.map(p => ({ ...p, date: customDate ?? no() })),
      NoteSource.Html,
    );

    console.log("*** HTML import completed successfully:");
    console.log(acceptedFiles[0].name);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({ onDrop });

  return (
    <div
      {...getRootProps()}
      style={{ height: 100, width: 500 }}
      className="border my-5 bg-pink-700"
    >
      <input {...getInputProps()} />
      <p className="m-1">
        {isDragActive
          ? "Drop the files here ..."
          : "Drag 'n' drop some files here, or click to select files"}
      </p>
    </div>
  );
}

async function readFullFile(file: File): Promise<string> {
  return new Promise(resolve => {
    const reader2 = new FileReader();
    reader2.onload = event => {
      Assert.hard(hasValue(event.target));
      resolve(event.target.result as string);
    };
    reader2.readAsText(file);
  });
}

export function getNodeListFromHtml(htmlContent: string) {
  const topNode = getTopNodeFromString(htmlContent);

  return buildNodeArray(topNode);
}

function getTopNodeFromString(htmlContent: string) {
  return parser.parse(htmlContent);
}

function buildNodeArray(elem: parser.Node) {
  const nodeList: parser.Node[] = [];
  addToNodeArray(elem, nodeList);
  return nodeList;
}

function addToNodeArray(elem: parser.Node, elemList: parser.Node[]) {
  if (elem.childNodes.length === 0 && elem.rawText.trim().length > 0) {
    elemList.push(elem); // forgive me for I have mutated
  } else {
    elem.childNodes.forEach(node => addToNodeArray(node, elemList));
  }
}

function getNoteInputForNode(
  node: parser.Node,
  acc: HtmlParserAcc,
): NoteInput | null {
  if (node.parentNode.attributes.class !== "noteText") {
    return null;
  }

  if (acc.nextNoteType === NoteTextType.WrittenNote) {
    return {
      noteText: node.rawText.trim(),
      highlightText: acc.activeHighlight?.trim() ?? undefined,
      sourceTitle: acc.sectionHeader.trim(),
    };
  }

  return null;
}

function getClassNameForTextNode(textNode: parser.Node): string {
  return textNode.parentNode.attributes.class;
}

function isWrittenNoteText(rawText: string | undefined) {
  if (!hasValue(rawText)) {
    return false;
  }

  const trimmed = rawText.trim();
  return /^Note -/.test(trimmed) || /^Nota -/.test(trimmed);
}

function isWrittenNoteHeading(node: parser.Node): boolean {
  const className = getClassNameForTextNode(node);
  return className === "noteHeading" && isWrittenNoteText(node.rawText);
}

export function getNoteInputsFromNodeList(
  nodeList: parser.Node[],
  shouldIgnoreSections: boolean,
): NoteInput[][] {
  const initialAcc: HtmlParserAcc = {
    sectionHeader: getBookTitle(nodeList),
    nextNoteType: NoteTextType.NoneSpecified,
    activeHighlight: null,
    sectionNoteInputLists: [[]],
  };

  const result = nodeList.reduce<HtmlParserAcc>((acc, currNode, index) => {
    const sectionNoteInputLists = [...acc.sectionNoteInputLists];
    const noteInput = getNoteInputForNode(currNode, acc);

    let newActiveHighlight: string | null = acc.activeHighlight;
    if (hasValue(noteInput)) {
      sectionNoteInputLists.slice(-1)[0].push(noteInput);
      newActiveHighlight = null; // consumed
    }

    const className = getClassNameForTextNode(currNode);
    const isHighlight =
      className === "noteText" && acc.nextNoteType === NoteTextType.Highlight;
    const isNextNodeWrittenNote = isWrittenNoteText(
      nodeList[index + 1]?.rawText,
    );

    // New active highlight if called for
    newActiveHighlight =
      isHighlight && isNextNodeWrittenNote
        ? currNode.rawText.trim()
        : newActiveHighlight;

    if (isHighlight && !isNextNodeWrittenNote) {
      // Save highlight immediately - not part of a note
      const highlightNoteInput: NoteInput = {
        highlightText: currNode.rawText,
        sourceTitle: acc.sectionHeader,
      };

      const hasWhitespace: boolean = /\s/.test(currNode.rawText.trim());
      if (hasWhitespace) {
        // Don't save lone single-word highlights; these are usually something
        // like .n. text
        sectionNoteInputLists.slice(-1)[0].push(highlightNoteInput);
      }
    }

    const nextNoteType: NoteTextType = /^highlight_/.test(className)
      ? NoteTextType.Highlight
      : isWrittenNoteHeading(currNode)
        ? NoteTextType.WrittenNote
        : acc.nextNoteType;

    let sectionHeader: string = acc.sectionHeader;

    if (
      !shouldIgnoreSections &&
      (isNewSection(currNode) || index === nodeList.length - 1)
    ) {
      // New section OR end of file
      sectionHeader = currNode.rawText.trim();

      if (sectionNoteInputLists.slice(-1)[0].length > 0) {
        // new section if last not empty
        sectionNoteInputLists.push([]);
      }
    }

    return {
      activeHighlight: newActiveHighlight,
      nextNoteType,
      sectionHeader,
      sectionNoteInputLists,
    };
  }, initialAcc);

  return result.sectionNoteInputLists.filter(l => l.length > 0);
}

function isNewSection(textNode: parser.Node) {
  return textNode.parentNode?.attributes.class === "sectionHeading";
}

export function getPatchesForSection(
  noteInputList: NoteInput[],
  shouldIgnoreSections: boolean,
):
  | { patches: MdPatch[]; errorResult: null }
  | { patches: null; errorResult: { invalidTags: string[] } } {
  const shouldAllowUniversal =
    !shouldIgnoreSections || config.shouldAllowAllInSectionless;

  // One-off tags are already separated by getSortedTagsForInput, so
  // grouping and ALL work correctly with one-off
  const taggedInputResults = noteInputList.map(input =>
    getTaggedInputs(input, {
      shouldAllowUniversal,
      shouldAllowUntaggedPatches: config.shouldAllowUntaggedPatchesInHtml,
    }),
  );

  const invalidTags = taggedInputResults.flatMap(r => r.invalidTags ?? []);

  if (taggedInputResults.some(t => hv(t.invalidTags))) {
    return {
      errorResult: { invalidTags },
      patches: null,
    };
  }

  const universalTags = taggedInputResults
    .flatMap(r => r.taggedInputs?.flatMap(input => input.universalTags))
    .filter<string>((t): t is string => hv(t));

  const untaggedResults = taggedInputResults
    .flatMap(r => r.taggedInputs!)
    .filter(input => input.localTags.length === 0);

  if (untaggedResults.length > 0 && universalTags.length === 0) {
    // This IS used as of Feb 2024
    console.log("Untagged found:");
    untaggedResults.forEach(r => console.log(r.input));
    return { patches: null, errorResult: { invalidTags: [] } };
  }

  const inputTagGroups = taggedInputResults
    .flatMap(r => r.taggedInputs)
    .map(r => r?.localTags ?? [])
    .filter(t => t.length > 0);

  const taggedInputs = taggedInputResults.flatMap(
    // Already verified above that there are no error results, so all
    // taggedInputs fields are non-null
    r => r.taggedInputs as TaggedInput[],
  );

  const { tagsToGroup } = getTagsToGroup(inputTagGroups, universalTags);

  if (universalTags.length === 0 && tagsToGroup.length === 0) {
    // Just treat normally
    return {
      patches: taggedInputs.map(getMdPatchForTaggedInput),
      errorResult: null,
    };
  }

  const universalPatch = getUniversalPatch(universalTags, noteInputList);

  // number of patches should be: one All patch, and then one patch for each
  // note that does NOT have all

  const individualPatchesToSave = getIndividualPatches({
    tagsToRemove: [...tagsToGroup, ...universalTags],
    taggedInputs,
  });

  return {
    patches: [
      ...(hv(universalPatch) ? [universalPatch] : []),
      ...getGroupPatches(tagsToGroup, taggedInputs),
      ...individualPatchesToSave,
    ],
    errorResult: null,
  };
}

export function getGroupPatches(
  groupTags: string[],
  taggedInputs: TaggedInput[],
): MdPatch[] {
  return groupTags.map<MdPatch>(groupTag => {
    const filteredInputs = taggedInputs.filter(({ localTags: tags }) =>
      includesBySynonyms(groupTag, tags),
    );

    Assert.hard(filteredInputs.length > 0, "No inputs!");
    const sourceTitle = filteredInputs[0].input.sourceTitle;
    Assert.hard(hv(sourceTitle) && sourceTitle.length > 0, "No title!");

    const { joplinNoteIds, joplinFolderIds } = getIdsFromTags([groupTag]);

    return {
      contentMd: getGroupedNoteMd(
        filteredInputs.map(({ input }) => input),
        sourceTitle,
      ),
      date: new Date(),
      invalidTags: [],
      joplinNoteIds,
      joplinFolderIds,
    };
  });
}

export function getIndividualPatches({
  tagsToRemove,
  taggedInputs,
}: {
  tagsToRemove: string[];
  taggedInputs: TaggedInput[];
}) {
  const allIdsToRemove = getIdsFromTags(tagsToRemove);
  return taggedInputs
    .flatMap<MdPatch | null>(({ input, localTags: tags }) => {
      const patch = getMdPatchForTaggedInput({
        input,
        localTags: tags,
        universalTags: [],
      });

      const filteredJoplinNoteIds = patch.joplinNoteIds.filter(
        id => !allIdsToRemove.joplinNoteIds.includes(id),
      );
      const filteredJoplinFolderIds = patch.joplinFolderIds.filter(
        id => !allIdsToRemove.joplinFolderIds.includes(id),
      );

      if ([...filteredJoplinNoteIds, ...filteredJoplinFolderIds].length === 0) {
        return null;
      }

      return {
        ...patch,
        joplinNoteIds: filteredJoplinNoteIds,
        joplinFolderIds: filteredJoplinFolderIds,
      };
    })
    .filter<MdPatch>((p): p is MdPatch => hasValue(p));
}

function getUniversalPatch(
  universalTags: string[],
  noteInputList: NoteInput[],
): MdPatch | null {
  if (universalTags.length === 0) {
    return null;
  }
  const { joplinNoteIds, joplinFolderIds } = getIdsFromTags(universalTags);
  const sourceTitle = noteInputList[0].sourceTitle;
  Assert.hard(hv(sourceTitle) && sourceTitle.length > 0);

  return {
    contentMd: getGroupedNoteMd(noteInputList, sourceTitle),
    date: new Date(),
    joplinNoteIds,
    joplinFolderIds,
  };
}

export function getGroupedNoteHtml(
  noteInputs: NoteInput[],
  sourceTitle: string,
) {
  const html = noteInputs.reduce<string>((acc, input) => {
    const highlightHtml = hasValue(input.highlightText)
      ? `${htmlFromContent(input.highlightText, "&gt; ")}`
      : "";
    const noteHtml = hasValue(input.noteText)
      ? `${htmlFromContent(input.noteText)}`
      : "";

    return `${acc}${
      highlightHtml === "" ? "" : insertBrInsideLastP(highlightHtml)
    }${noteHtml === "" ? "" : insertBrInsideLastP(noteHtml)}`;
  }, ``);

  // Remove final unneeded <br /> and re-close last </p>, 10 chars
  return `${html.slice(
    0,
    html.length - 10,
  )}</p>${P}---</p>${P}<em>Source: ${sourceTitle}</em></p>`;
}

export function getGroupedNoteMd(noteInputs: NoteInput[], sourceTitle: string) {
  const md = noteInputs.reduce<string>((acc, input) => {
    const highlightMd = hasValue(input.highlightText)
      ? `${mdFromContent(input.highlightText, "> ")}`
      : "";
    const noteMd = hasValue(input.noteText)
      ? `${mdFromContent(input.noteText)}`
      : "";

    return `${acc}${
      highlightMd === "" ? "" : `${highlightMd}\n\n`
    }${noteMd === "" ? "" : `${noteMd}\n\n`}`;
  }, ``);

  // Remove final unneeded <br /> and re-close last </p>, 10 chars
  return `${md}${getSourceFooter(sourceTitle)}`;
}

export function getAllSynonyms(tags: string[]): string[] {
  const synonymsWithDupes = tags.flatMap(t => {
    const set = TAG_ID_PAIRINGS.find(p =>
      p.tags.map(tag => tag.toLowerCase()).includes(t),
    );

    return Assert.hasValue(set?.tags, `Not found tag: '${t}'`);
  });

  const synonyms = Array.from(new Set(synonymsWithDupes));

  if (synonyms.filter(t => !hasValue(t)).length !== 0) {
    throw new Error(`Empty tag: ${synonyms.toString()}`);
  }

  return synonyms;
}

// Within a section, all synonyms are grouped.  E.g. if there are three
// inputs with "generaltravel" then they will appear as one dated entry
// in that journal
export function getTagsToGroup(
  inputTagGroups: string[][],
  universalTags: string[],
): {
  tagsToGroup: string[];
} {
  const { tagsToGroup } = inputTagGroups.reduce(
    (acc, currTags) => {
      return {
        tags: [...acc.tags, ...currTags],
        tagsToGroup: [
          ...acc.tagsToGroup,
          ...currTags.filter(t =>
            getAllSynonyms([t]).some(syn => acc.tags.includes(syn)),
          ),
        ],
      };
    },
    { tagsToGroup: [] as string[], tags: [] as string[] },
  );

  return {
    tagsToGroup: dedupBySynonyms(removeBySynonyms(tagsToGroup, universalTags)),
  };
}

export function dedupBySynonyms(tags: string[]) {
  const result = tags.reduce<string[]>((prevTags, tag) => {
    const synonyms = getAllSynonyms([tag]);
    const shouldInclude = !prevTags.some(prevTag => synonyms.includes(prevTag));
    return [...prevTags, ...(shouldInclude ? [tag] : [])];
  }, []);

  return result;
}

export function includesBySynonyms(tag: string, tagList: string[]) {
  const tagSynonyms = getAllSynonyms([tag]);

  return getAllSynonyms(tagList).some(t => tagSynonyms.includes(t));
}

export function removeBySynonyms(tagList: string[], removeList: string[]) {
  return tagList.filter(t => {
    const synonyms = getAllSynonyms(removeList);
    return !synonyms.includes(t);
  });
}

export function shouldIgnoreSectionsInNodeList(nodeList: parser.Node[]) {
  for (const node of nodeList) {
    if (
      (node.rawText.trim().toLowerCase() === "<EMAIL>" ||
        node.rawText.trim() === "P2K") &&
      node.parentNode.rawTagName === "div" &&
      node.parentNode.getAttribute("class") === "authors"
    ) {
      return false; // p2k export has proper sections
    }
  }

  return true; // book or sectionless article export - ignore sections
}

export function getBookTitle(nodeList: parser.Node[]) {
  for (const node of nodeList) {
    if (
      node.parentNode.rawTagName === "div" &&
      node.parentNode.getAttribute("class") === "bookTitle"
    ) {
      const bookTitle = node.rawText.trim();
      Assert.hard(hv(bookTitle), "Book title is empty");

      return bookTitle;
    }
  }

  throw new Error("no book title found");
}

interface HtmlParserAcc {
  sectionHeader: string;
  nextNoteType: NoteTextType;
  activeHighlight: string | null;
  sectionNoteInputLists: NoteInput[][];
}
