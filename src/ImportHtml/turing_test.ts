export const turing_test_html = `
<html>

<body>
  <div class="bodyContainer">
    <div class="notebookFor">
      Notebook Export
    </div>
    <div class="bookTitle">
      Alan Turing: A Life From Beginning to End (World War 2 Biographies)
    </div>
    <div class="authors">
      History, Hourly
    </div>
    <div class="citation">
    </div>
    <hr />
    <div class="sectionHeading">
      Childhood and Adolescence
    </div>

    <div class="noteHeading">
      Highlight(<span class="highlight_yellow">yellow</span>) - Page 39 ·
      Location 384
    </div>
    <div class="noteText">
      In 2017, the so-called Alan Turing law provided full pardons for men
      convicted for performing homosexual acts.
    </div>
    <div class="noteHeading">
      Note - Page 39 · Location 385
    </div>
    <div class="noteText">
      That is suuuuuper recent wow. .z.antigay
    </div>

    <div class="noteHeading">
    Highlight(<span class="highlight_yellow">yellow</span>) - Page 4 · Location 47
    </div>
    <div class="noteText">
    The Wards were from a military background and issued to their charges their own brand of military discipline, which was something that young <PERSON> simply abhorred.
    </div><div class="noteHeading">
    Note - Page 4 · Location 48
    </div>
    <div class="noteText">
    Surrogate parents... So far i relate.e. .z.b
    </div>

    <!-- example from Hunter Gatherer -->
    <div class="noteHeading">
    Highlight(<span class="highlight_yellow">yellow</span>) - Página xiii ·
    Posición 135
  </div>
  <div class="noteText">
    Science is a method that oscillates between induction and deduction—
  </div>
  <div class="noteHeading">
    Note - Page xiii · Posición 135
  </div>
  <div class="noteText">
    .z.u define this
  </div>

  </div>
</body>

</html>`;
