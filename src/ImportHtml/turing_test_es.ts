export const turing_test_es_html = `
<html>

<body>
  <div class="bodyContainer">
    <div class="notebookFor">
      Exportación de bloc de notas
    </div>
    <div class="bookTitle">
      Alan Turing: A Life From Beginning to End (World War 2 Biographies)
    </div>
    <div class="authors">
      History, Hourly
    </div>
    <div class="citation">
    </div>
    <hr />
    <div class="sectionHeading">
      Childhood and Adolescence
    </div>
    <div class="noteHeading">
      Subrayar(<span class="highlight_yellow">amarillo</span>) - Página 39 ·
      Posición 384
    </div>
    <div class="noteText">
      In 2017, the so-called Alan Turing law provided full pardons for men
      convicted for performing homosexual acts.
    </div>
    <div class="noteHeading">
      Nota - Page 39 · Location 385
    </div>
    <div class="noteText">
      That is suuuuuper recent wow. .z.antigay
    </div>

    <div class="noteHeading">
    Subrayar(<span class="highlight_yellow">yellow</span>) - Page 4 · Location 47
    </div>
    <div class="noteText">
    The Wards were from a military background and issued to their charges their own brand of military discipline, which was something that young Alan Turing simply abhorred.
    </div><div class="noteHeading">
    Nota - Page 4 · Location 48
    </div>
    <div class="noteText">
    Surrogate parents... So far i relate.e. .z.b
    </div>


    <!-- example from Hunter Gatherer -->
    <div class="noteHeading">
    Subrayar(<span class="highlight_yellow">amarillo</span>) - Página xiii ·
    Posición 135
  </div>
  <div class="noteText">
    Science is a method that oscillates between induction and deduction—
  </div>
  <div class="noteHeading">
    Nota - Página xiii · Posición 135
  </div>
  <div class="noteText">
    .z.u define this
  </div>

  </div>
</body>

</html>`;
