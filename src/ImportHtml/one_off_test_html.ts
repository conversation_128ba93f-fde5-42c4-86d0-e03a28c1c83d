export const one_off_test_html = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "XHTML1-s.dtd">
<html xmlns="http://www.w3.org/TR/1999/REC-html-in-xml" xml:lang="en" lang="en">
<body>
  <div class="bodyContainer">
    <div class="notebookFor">
      Notebook Export
    </div>
    <div class="bookTitle">
		Testing some one-offs
    </div>
    <div class="authors">
      <PERSON>
    </div>
    <div class="citation"></div>
    <hr />
    <div class="sectionHeading">
      One-off test
    </div>

    <div class="noteHeading">
      Highlight(<span class="highlight_yellow">yellow</span>) - Chapter 1: The
      breaks
    </div>
    <div class="noteText">
    high1
    </div>
    <div class="noteHeading">
      Note - Chapter 1: The breaks
    </div>
    <div class="noteText">
      note1 @@all @@u
      @@autoparse @@o
    </div>

    <div class="noteHeading">
      Highlight(<span class="highlight_yellow">yellow</span>) - Chapter 1: The
      breaks
    </div>
    <div class="noteText">
    high2
    </div>
    <div class="noteHeading">
      Note - Chapter 1: The breaks
    </div>
    <div class="noteText">
    @@oneoff @@spain first part\n@@b second part
    </div>

    <div class="noteHeading">
      Note - Chapter 1: The breaks
    </div>
    <div class="noteText">
    final part @@p7
    </div>

    <div class="noteHeading">
    Note - Chapter 1: The breaks
  </div>
  <div class="noteText">
  one-off matching ALL does not save alone @@oneoff @@u
  
  this saves to all too
  </div>


  <div class="noteHeading">
  Note - Chapter 1: The breaks
</div>
<div class="noteText">
some text that combines @@p7\nand one-off that also combines @@o@@b
</div>


  </div>
</body>
</html>`;
