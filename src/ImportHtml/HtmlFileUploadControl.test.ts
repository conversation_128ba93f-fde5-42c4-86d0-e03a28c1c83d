import { getIdsFromTags, MdPatch } from "../Components/getPatchForTaggedInput";
import { NoteInput } from "../NoteInput";
import { Assert, BR, hasValue, hv, P } from "../Tools";
import {
  getAllSynonyms,
  getNodeListFromHtml,
  getNoteInputsFromNodeList,
  getPatchesForSection,
  getTagsToGroup,
  dedupBySynonyms,
  removeBySynonyms,
  getGroupPatches,
  includesBySynonyms,
  getIndividualPatches,
  shouldIgnoreSectionsInNodeList,
  getBookTitle,
  getGroupedNoteHtml,
} from "./HtmlFileUploadControl";
import { p2k_test_html } from "./p2k_test_html";
import { kindle_book_test_html } from "./kindle_book_test_html";
import { turing_test_html } from "./turing_test";
import { turing_test_es_html } from "./turing_test_es";
import { hunterGathererTestHtml } from "./hunterGathererTestHtml";
import { one_off_test_html } from "./one_off_test_html";
import { getTaggedInputs } from "../getTags";
import { p2k_test_html_with_untagged } from "./p2k_test_html_with_untagged";
import { sortBy } from "../Comparable";
import {
  AUTOPARSE_NOTE_ID,
  B_FOLDER_ID,
  BOLDNESS_NOTE_ID,
  CDMX_NOTE_ID,
  MANAGEMENT_NOTE_ID,
  P7_FOLDER_ID,
  P_FOLDER_ID,
  REMOTE_WORK_NOTE_ID,
  SPAIN_NOTE_ID,
  URGENT_FOLDER_ID,
  WEEKLY_FOLDER_ID,
} from "../Joplin/TestConstants";
import { TAG_ID_PAIRINGS } from "../PairingHelpers";

test("getSectionPatches with simple All", () => {
  const sourceTitle = "Some source";
  const sectionNoteInputs: NoteInput[] = [
    {
      highlightText: "high1",
      noteText: "note1",
      sourceTitle,
    },
    {
      highlightText: "high2",
      noteText: `@@all @@remotework @@b @@cdmx let's save this! @@a

      @@all also all for @@business

      (But this should ONLY be ;;w, not 'all')`,
      sourceTitle,
    },
    {
      noteText: "not a tag",
      sourceTitle,
    },
  ];

  const { patches } = getPatchesForSection(sectionNoteInputs, false);
  console.log(patches);

  expect(patches?.length).toEqual(2); // one for all, one for ;;w

  // A patch for just week
  expect(patches?.[1]?.joplinFolderIds).toEqual([WEEKLY_FOLDER_ID]);
  expect(patches?.[0].contentMd).toContain("high1");
  expect(patches?.[0].contentMd).toContain("high2");
  expect(patches?.[0].contentMd).toContain("not a tag");
});

test("get section patches with no 'all'", () => {
  const sourceTitle = "Some source";
  const sectionNoteInputs: NoteInput[] = [
    {
      highlightText: "high1",
      noteText: "note1 @@remotework",
      sourceTitle,
    },
    {
      highlightText: "high2",
      noteText: "@@b",
      sourceTitle,
    },
    {
      noteText: "@@boldness",
      sourceTitle,
    },
  ];

  const { patches } = getPatchesForSection(sectionNoteInputs, false);

  expect(patches?.length).toEqual(3);
});

test("getSectionPatches with 'all' and one-off", () => {
  const sourceTitle = "Some source";
  const sectionNoteInputs: NoteInput[] = [
    {
      highlightText: "high1",
      noteText: "note1 @@bold", // should be deduped WITH a synonym
      sourceTitle,
    },
    {
      highlightText: "high2",
      noteText: "@@all @@remotework @@b @@cdmx let's save this!",
      sourceTitle,
    },
    {
      noteText: "@@management @@remotework expertise",
      sourceTitle,
    },
    {
      highlightText: "high3",
      sourceTitle,
    },
    {
      highlightText: "high4",
      noteText: "@@boldness @@all @@a",
      sourceTitle,
    },
    {
      highlightText: "another hl",
      noteText: "JUST save this in @@remotework as a TOTALLY SEPARATE note...",
      sourceTitle,
    },
    {
      highlightText: "hl",
      noteText: "separate @@management note",
      sourceTitle,
    },
    {
      highlightText: "something with a unique tag",
      noteText: "dum dee dum @@p\n\nand a @@o one-off @@travelgen",
      sourceTitle,
    },
  ];

  const { patches } = getPatchesForSection(sectionNoteInputs, false);
  Assert.hard(hv(patches));
  // One "all" and one "management" patch
  expect(patches.length).toEqual(4);

  expect(patches[0].contentMd).toMatch("high1");
  expect(patches[0].contentMd).toMatch("note1");
  expect(patches[0].contentMd).toMatch("high2");
  expect(patches[0].contentMd).toMatch("let's save this!");
  expect(patches[0].contentMd).toMatch("high3");
  expect(patches[0].contentMd).toMatch("high4");
  expect(patches[0].contentMd).toMatch("expertise");

  expect(patches[0].joplinFolderIds).toEqual([B_FOLDER_ID]);
  expect(patches[0].joplinNoteIds).toEqual([
    CDMX_NOTE_ID,
    REMOTE_WORK_NOTE_ID,
    BOLDNESS_NOTE_ID,
  ]);

  expect(patches[1].contentMd).toMatch("expertise");
  expect(patches[1].joplinNoteIds).toEqual([MANAGEMENT_NOTE_ID]);
  expect(patches[1].joplinFolderIds).toEqual([]);

  expect(patches[2].joplinFolderIds).toEqual([P_FOLDER_ID]);
  // general tag includes the one-off
  expect(patches[2].contentMd).toContain("travelgen");

  // but one-off does not include the general stuff
  expect(patches[3].contentMd).toContain("travelgen");
  expect(patches[3].contentMd).not.toContain("dum dee dum");
});

test("HTML parse rejects if untagged", async () => {
  const nodeList = getNodeListFromHtml(p2k_test_html_with_untagged);
  const sectionNoteInputs: NoteInput[][] = getNoteInputsFromNodeList(
    nodeList,
    false,
  );

  const results = sectionNoteInputs.map(inputList =>
    getPatchesForSection(inputList, false),
  );

  expect(
    results.some(r => r.errorResult?.invalidTags.length === 0),
  ).toBeTruthy();
});

test("get note list from P2K HTML", async () => {
  const nodeList = getNodeListFromHtml(p2k_test_html);
  const sectionNoteInputs: NoteInput[][] = getNoteInputsFromNodeList(
    nodeList,
    false,
  );
  expect(sectionNoteInputs.length).toEqual(2); // 2 sections

  const noteInputs: NoteInput[] = sectionNoteInputs.flat();
  expect(noteInputs.length).toEqual(8);

  const expectedResult: {
    highlightMatch?: string;
    noteMatch?: string;
  }[] = [
    { highlightMatch: "Ressler told" }, // remotework, deduped
    { highlightMatch: "when, where" },
    { highlightMatch: "what they want" },
    { highlightMatch: "When work goes" },
    { highlightMatch: "Meanwhile, " },
    { noteMatch: "Yup. I've seen" },
    // Next article
    { highlightMatch: "In sum, there" },
    { noteMatch: "wd def like to listen" },
  ];

  expectedResult.forEach((r, index) => {
    // log index for easy debugging
    expect(noteInputs[index].highlightText ?? "").toMatch(
      r.highlightMatch ?? "",
    );
    expect(noteInputs[index].noteText ?? "").toMatch(r.noteMatch ?? "");
  });
});

const kindleBookTitle =
  "Life in the Fasting Lane: How to Make Intermittent Fasting a Lifestyle";

test("Hunter Gatherer - broken early 2022", () => {
  const nodeList = getNodeListFromHtml(hunterGathererTestHtml);
  const sectionNoteInputs: NoteInput[][] = getNoteInputsFromNodeList(
    nodeList,
    true /*ignoreSections*/,
  );

  expect(sectionNoteInputs.length).toBe(1);
  expect(sectionNoteInputs[0].length).toBe(2);

  const { patches } = getPatchesForSection(sectionNoteInputs[0], true);
  Assert.hard(hv(patches));
  expect(patches.length).toBe(2);
  expect(patches[0].joplinFolderIds.length).toBe(1);
  expect(patches[0].joplinNoteIds.length).toBe(0);
  expect(patches[0].contentMd).toContain("Science is a method");
  expect(patches[0].contentMd).toContain("define this");
  expect(patches[1].joplinFolderIds.length).toBe(0);
  expect(patches[1].joplinNoteIds.length).toBe(1);
  expect(patches[1].contentMd).toContain("We thus generate");
  expect(patches[1].contentMd).toContain("Good framing");
});

test("turing book inputs - English UI", () => {
  testTuring(turing_test_html);
});

test("turing book inputs - Spanish UI", () => {
  testTuring(turing_test_es_html);
});

function testTuring(htmlInput: string) {
  const nodeList = getNodeListFromHtml(htmlInput);
  const sectionNoteInputs: NoteInput[][] = getNoteInputsFromNodeList(
    nodeList,
    true /*ignoreSections*/,
  );

  expect(sectionNoteInputs.length).toBe(1);
  expect(sectionNoteInputs[0].length).toBe(3);
  expect(sectionNoteInputs[0][0].highlightText).not.toBeUndefined();
  expect(sectionNoteInputs[0][0].noteText).not.toBeUndefined();

  const { patches } = getPatchesForSection(sectionNoteInputs[0], true);

  Assert.hard(hv(patches));
  expect(patches.length).toBe(3);
  expect(patches[0].joplinFolderIds.length).toBe(0);
  expect(patches[0].joplinNoteIds.length).toBe(1);
  expect(patches[1].joplinFolderIds.length).toBe(1);
  expect(patches[1].joplinNoteIds.length).toBe(0);
  expect(patches[2].contentMd).toContain("define this");
  expect(patches[2].contentMd).toContain("Science is a method");
}

test("node list from Kindle book HTML", () => {
  const nodeList = getNodeListFromHtml(kindle_book_test_html);
  const sectionNoteInputs: NoteInput[][] = getNoteInputsFromNodeList(
    nodeList,
    true,
  );

  // book has one section
  expect(sectionNoteInputs.length).toEqual(1);
  // Before combining.
  // should ignore lone single-word highlight but not highlight with spaces
  expect(sectionNoteInputs[0].length).toEqual(3);

  // All have same soure title - the book title.  Ignore chapter titles.
  expect(
    sectionNoteInputs[0].some(
      input => !input.sourceTitle!.includes(kindleBookTitle),
    ),
  ).toBe(false);

  const { patches } = getPatchesForSection(sectionNoteInputs[0], true);

  // Has a 'b' note and a 'nutrition' note, with another combined in
  expect(patches?.length).toEqual(2);
});

test("getGroupTags", () => {
  // Each line represents tags from a single input
  const inputTagGroups = [
    ["bold"],
    ["travelgen", "cdmx"],
    ["cdmx", "nyc", "mex"],
    ["nyc", "travelgeneral"],
    ["nyc", "boldness"],
    ["mexico"], // will not appear as grouped - not present in any other notes
  ];

  const universalTags = ["travelgen", "cdmx", "mexico"];

  const { tagsToGroup } = getTagsToGroup(inputTagGroups, universalTags);

  // SECOND group tag will be found (first dupe) so "boldness" not "bold"
  // ...and this should skip the universal synonyms
  expect(tagsToGroup).toEqual(["nyc", "boldness"]);
});

test("getAllSynonyms", async () => {
  const tags = ["bold", "boldness", "airquality"];
  const synonyms = getAllSynonyms(tags);
  const boldTags = TAG_ID_PAIRINGS.find(t => t.tags.includes("boldness"))?.tags;
  const aqTags = TAG_ID_PAIRINGS.find(t => t.tags.includes("airquality"))?.tags;

  Assert.hard(
    hasValue(boldTags) && hasValue(aqTags),
    "Tags have changed - could not find test tags bold, aq",
  );

  expect(synonyms).toEqual([...boldTags, ...aqTags]);
});

test("partial-all feature", () => {
  const groupTagSectionInputs: NoteInput[] = [
    {
      highlightText: "high1",
      noteText: "@@bold @@All note1", // should be deduped WITH a synonym
      sourceTitle: "t",
    },
    {
      highlightText: "high2",
      noteText: "@@Musk @@p note2",
      sourceTitle: "t",
    },
    {
      highlightText: "high3",
      noteText: "@@elonmusk @@amazon note3",
      sourceTitle: "t",
    },
    {
      highlightText: "high4",
      noteText: "@@PMEM @@amazonco note4",
      sourceTitle: "t",
    },
    {
      highlightText: "high5",
      noteText: "@@all @@bmem @@boldness note5",
    },
  ];

  const { patches } = getPatchesForSection(groupTagSectionInputs, false);
  Assert.hard(hv(patches));
  expect(patches.length).toEqual(4);

  const { joplinFolderIds, joplinNoteIds } = getIdsFromTags([
    "bold",
    "musk",
    "p",
    "amazon",
  ]);

  const [allPatch, muskPatch, amazonPatch] = joplinNoteIds.map(
    id => patches.find(p => p.joplinNoteIds.includes(id))!,
  );

  const pPatch = patches.find(p =>
    p.joplinFolderIds.includes(joplinFolderIds[0]),
  )!;

  expect(allPatch.contentMd).toMatch("high1");
  expect(allPatch.contentMd).toMatch("note1");
  expect(allPatch.contentMd).toMatch("high2");
  expect(allPatch.contentMd).toMatch("note2");
  expect(allPatch.contentMd).toMatch("high3");
  expect(allPatch.contentMd).toMatch("note3");
  expect(allPatch.contentMd).toMatch("high4");
  expect(allPatch.contentMd).toMatch("note4");
  expect(allPatch.contentMd).toMatch("high5");
  expect(allPatch.contentMd).toMatch("note5");
  expect(allPatch.joplinNoteIds.length).toEqual(1);
  expect(allPatch.joplinFolderIds.length).toEqual(1);

  expect(muskPatch.contentMd).toMatch("high2");
  expect(muskPatch.contentMd).toMatch("note2");
  expect(muskPatch.contentMd).toMatch("high3");
  expect(muskPatch.contentMd).toMatch("note3");
  expect(muskPatch.contentMd).not.toMatch("note1");
  expect(muskPatch.contentMd).not.toMatch("note4");
  expect(muskPatch.joplinNoteIds.length).toEqual(1);
  expect(muskPatch.joplinFolderIds.length).toEqual(0);

  expect(pPatch.contentMd).toMatch("high2");
  expect(pPatch.contentMd).toMatch("note2");
  expect(pPatch.contentMd).toMatch("high4");
  expect(pPatch.contentMd).toMatch("note4");
  expect(pPatch.contentMd).not.toMatch("note1");
  expect(pPatch.contentMd).not.toMatch("note3");
  expect(pPatch.joplinNoteIds.length).toEqual(0);
  expect(pPatch.joplinFolderIds.length).toEqual(1);

  expect(amazonPatch.contentMd).toMatch("high3");
  expect(amazonPatch.contentMd).toMatch("note3");
  expect(amazonPatch.contentMd).toMatch("high4");
  expect(amazonPatch.contentMd).toMatch("note4");
  expect(amazonPatch.contentMd).not.toMatch("note1");
  expect(amazonPatch.contentMd).not.toMatch("note2");
  expect(amazonPatch.joplinNoteIds.length).toEqual(1);
  expect(amazonPatch.joplinFolderIds.length).toEqual(0);
});

test("dedupBySynonym", () => {
  const tags = ["bold", "bach", "bachata", "bold", "cdmx"];
  const deduped = dedupBySynonyms(tags);
  expect(deduped).toEqual(["bold", "bach", "cdmx"]);
});

test("removeBySynonyms", () => {
  const tags = ["bold", "cdmx", "trav", "mtlnext", "nyc"];
  const remove = ["boldness", "travel", "nyc"];
  const final = removeBySynonyms(tags, remove);
  expect(final).toEqual(["cdmx", "mtlnext"]);
});

test("getGroupPatches", () => {
  // No dupes among these two arrays - already filtered out in previous code
  const groupTags = ["commute", "b"];
  const noteInputs: NoteInput[] = [
    { noteText: "note1 @@boldness @@commuting", sourceTitle: "t" },
    { noteText: "note2 @@bmem @@commute", sourceTitle: "t" },
    { noteText: "note3 @@boldly @@mtlnext", sourceTitle: "t" },
    { noteText: "note4 @@b @@amazon", sourceTitle: "t" },
  ];

  const taggedInputs = noteInputs.flatMap(
    input => getTaggedInputs(input).taggedInputs!,
  );
  const patches = getGroupPatches(groupTags, taggedInputs);

  expect(patches.length).toEqual(2);

  expect(patches[0].joplinNoteIds.length).toBe(1);
  expect(patches[0].joplinFolderIds.length).toBe(0);
  expect(patches[0].contentMd).toMatch("note1");
  expect(patches[0].contentMd).toMatch("note2");
  expect(patches[0].contentMd).not.toMatch("note3");
  expect(patches[0].contentMd).not.toMatch("note4");

  expect(patches[1].joplinNoteIds.length).toBe(0);
  expect(patches[1].joplinFolderIds.length).toBe(1);
  expect(patches[1].contentMd).not.toMatch("note1");
  expect(patches[1].contentMd).toMatch("note2");
  expect(patches[1].contentMd).not.toMatch("note3");
  expect(patches[1].contentMd).toMatch("note4");
});

test("includesBySynonyms", () => {
  expect(includesBySynonyms("bold", ["cdmx", "nyc", "workhours"])).toBe(false);
  expect(includesBySynonyms("bold", ["cdmx", "nyc", "boldness"])).toBe(true);
});

test("getIndividualPatches", () => {
  const tagsToRemove = ["b", "nyc"];
  const noteInputList: NoteInput[] = [
    {
      noteText: "note1 @@b",
    },
    {
      noteText: "note2 @@b @@padlock",
    },
  ];
  const patches = getIndividualPatches({
    tagsToRemove,
    taggedInputs: noteInputList.flatMap(
      input => getTaggedInputs(input).taggedInputs!,
    ),
  });

  expect(patches.length).toBe(1); // Should filter 'b'
  expect(patches[0].contentMd).toMatch("note2");
  expect(patches[0].contentMd).not.toMatch("note1");
  expect(patches[0].joplinNoteIds.length).toBe(1);
  expect(patches[0].joplinFolderIds.length).toBe(0);
});

const dataForIsSectioned = [
  {
    html: `<div class="authors">  P2K     </div>`,
    expected: false,
  },
  { html: `<div class="authors">  <EMAIL>  </div>`, expected: false },
  { html: `<div>P2K</div>`, expected: true },
  { html: `<div class="authors">  someone  </div>`, expected: true },
];

test("should ignore sections", () => {
  dataForIsSectioned.forEach(({ html, expected }) => {
    const nodeList = getNodeListFromHtml(html);
    const shouldIgnore = shouldIgnoreSectionsInNodeList(nodeList);
    expect(shouldIgnore).toBe(expected);
  });
});

test("getBookTitle", () => {
  const html = `<div class="bookTitle"> MYYYY title   </div>`;
  const nodeList = getNodeListFromHtml(html);
  const title = getBookTitle(nodeList);
  expect(title).toEqual("MYYYY title");

  const notFoundHtml = `${P}Not a title</p>`;
  expect(() => getBookTitle(getNodeListFromHtml(notFoundHtml))).toThrow();
});

test("no 'all' tag in sectionless", () => {
  // This is NYI - may not ever implement, exports tend to be long and grouping
  // would often be inappropriate

  expect(() =>
    getPatchesForSection(
      [{ noteText: "@@all @@sp", sourceTitle: "a test" }],
      true,
    ),
  ).toThrow();
  expect(() =>
    getPatchesForSection(
      [{ noteText: "@@all @@sp", sourceTitle: "a test" }],
      false,
    ),
  ).not.toThrow();
});

test("getCombinedNoteHtml", () => {
  const noteInputs: NoteInput[] = [
    { highlightText: "first highlight" },
    {
      highlightText: "second highlight (with text)\n\n\nand line break",
      noteText: "my text!",
    },
    { noteText: "just note" },
    { highlightText: "final highlight", noteText: "final text" },
  ];
  const finalStr = getGroupedNoteHtml(noteInputs, "MY SOURCE");

  expect(finalStr).toEqual(
    `${P}&gt; first highlight<br /></p>${P}&gt; second highlight (with text)</p>${P}&gt;</p>${P}&gt; and line break${BR}</p>${P}my text!${BR}</p>${P}just note${BR}</p>${P}&gt; final highlight${BR}</p>${P}final text</p>${P}---</p>${P}<em>Source: MY SOURCE</em></p>`,
  );
});

test("one-off test", () => {
  const nodeList = getNodeListFromHtml(one_off_test_html);
  const sectionNoteInputs: NoteInput[][] = getNoteInputsFromNodeList(
    nodeList,
    false /*ignoreSections*/,
  );

  // just one section
  expect(sectionNoteInputs.length).toBe(1);

  const patches = getPatchesForSection(
    sectionNoteInputs[0],
    false,
  ).patches?.toSorted(sortBy(p => p.contentMd));
  Assert.hard(hv(patches));

  const sourceFooter = `---\n_Source: One-off test_`;

  const base: Partial<MdPatch> = {
    joplinNoteIds: [],
    joplinFolderIds: [],
  };
  const expectedPatches: Partial<MdPatch>[] = [
    {
      ...base,
      contentMd: `> high1\n\n@@autoparse @@o\n\n${sourceFooter}`,
      joplinNoteIds: [AUTOPARSE_NOTE_ID],
    },
    {
      ...base,
      contentMd: `> high1\n\nnote1 @@all @@u\n@@autoparse @@o\n\n> high2\n\n@@oneoff @@spain first part\n@@b second part\n\nfinal part @@p7\n\none-off matching ALL does not save alone @@oneoff @@u\n\nthis saves to all too\n\nsome text that combines @@p7\nand one-off that also combines @@o@@b\n\n${sourceFooter}`,
      joplinFolderIds: [URGENT_FOLDER_ID],
    },
    {
      ...base,
      contentMd: `> high2\n\n@@oneoff @@spain first part\n\n${sourceFooter}`,
      joplinNoteIds: [SPAIN_NOTE_ID],
    },
    {
      ...base,
      contentMd: `> high2\n\n@@oneoff @@spain first part\n@@b second part\n\nand one-off that also combines @@o@@b\n\n${sourceFooter}`,
      joplinFolderIds: [B_FOLDER_ID],
    },
    {
      ...base,
      contentMd: `final part @@p7\n\nsome text that combines @@p7\nand one-off that also combines @@o@@b\n\n${sourceFooter}`,
      joplinFolderIds: [P7_FOLDER_ID],
    },
  ];
  expect(patches?.length).toEqual(5);

  expectedPatches.forEach((p, i) => {
    // Compare contentHtml separately so we get a detailed string diff on error
    expect(patches[i].contentMd).toEqual(p.contentMd);
    expect(patches[i]).toMatchObject(p);
  });
});

test("getPatchesForSection rejects untagged with no universal", () => {
  const inputs: NoteInput[] = [
    {
      noteText: "Untagged",
    },
    { noteText: "tagged @@autoparse" },
  ];

  expect(getPatchesForSection(inputs, false)).toEqual({
    patches: null,
    errorResult: { invalidTags: [] },
  });
});

test("allow untagged", () => {
  expect(
    getTaggedInputs(
      { noteText: "Untagged" },
      { shouldAllowUntaggedPatches: true },
    ),
  ).toEqual({ taggedInputs: [], invalidTags: null });
});
