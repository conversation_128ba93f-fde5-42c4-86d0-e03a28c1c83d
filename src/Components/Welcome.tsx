// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
// <WelcomeSnippet>
import React, { useContext, useEffect, useState } from "react";
import { config } from "../Config";
import { hv } from "../Tools";
import { AuthComponentContext } from "./AuthProvider";
import { MainControls } from "./PageSearch";
import { validateConfig } from "../validateConfig";

function WelcomeContent() {
  const { isAuthenticated, login } = useContext(AuthComponentContext);
  if (isAuthenticated || !config.shouldLogInToMsGraph) {
    return null;
  }

  // Not authenticated, present a sign in button
  return (
    <button
      style={{ backgroundColor: "blue", color: "white" }}
      onClick={e => {
        e.preventDefault();
        login();
      }}
    >
      Click here to sign in
    </button>
  );
}

export function Welcome() {
  const [configErrors, setConfigErrors] = useState<string[] | null>(null);
  useEffect(() => {
    // Validate config after fetching latest tagNotePairs
    const errorMessages = validateConfig();
    setConfigErrors(errorMessages);
  }, []);

  return (
    <>
      {hv(configErrors) && (
        <>
          <h3 className="bg-red-200">Tag validation errors:</h3>
          {configErrors.map(msg => (
            <p className="bg-red-200" key={msg}>
              {msg}
            </p>
          ))}
        </>
      )}
      {config.shouldAllowUntaggedPatchesInHtml === true && (
        <p className="bg-yellow-300 p-2">Allowing untagged patches in HTML</p>
      )}
      {config.shouldAllowAllInSectionless === true && (
        <p className="bg-yellow-300 p-2">Allowing 'all' in sectionless</p>
      )}
      <WelcomeContent />
      <MainControls />
    </>
  );
}
