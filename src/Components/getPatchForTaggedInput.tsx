import * as DateFns from "date-fns";
import { NoteInput } from "../NoteInput";
import { Assert, getSourceFooter, hasValue, hv, mdFromContent } from "../Tools";
import { TAG_ID_PAIRINGS } from "../PairingHelpers";

export interface MdPatch {
  contentMd: string;
  joplinNoteIds: string[];
  joplinFolderIds: string[];
  date: Date;
}

// Tagged; also indicates One-Off Notes have been divided out
export interface TaggedInput {
  input: NoteInput;
  localTags: string[];
  universalTags: string[];
}

export function getMdPatchForTaggedInput({
  input,
  localTags,
}: TaggedInput): MdPatch {
  const highlightMd = hasValue(input.highlightText)
    ? mdFromContent(input.highlightText, "> ")
    : "";

  const noteMd =
    !hasValue(input.noteText) || input.noteText.trim().length === 0
      ? ""
      : mdFromContent(input.noteText);

  const sourceMd = hasValue(input.sourceTitle)
    ? getSourceFooter(input.sourceTitle)
    : "";

  const contentMd = `${highlightMd}${
    highlightMd.length > 0 && noteMd.length > 0 ? "\n\n" : ""
  }${noteMd}\n\n${sourceMd}`.trim(); // trim in case no source

  const { joplinNoteIds, joplinFolderIds } = getIdsFromTags(localTags);

  return {
    contentMd,
    date: hasValue(input.date) ? DateFns.parseISO(input.date) : new Date(),
    joplinNoteIds,
    joplinFolderIds,
  };
}

// Any tag list (not labeled as Universal) will only contain valid tags in the
// list, and not "o" or "a"
export function getIdsFromTags(tags: string[]) {
  const joplinNoteIdSet = new Set<string>();
  const joplinFolderIdSet = new Set<string>();

  tags.forEach(tag => {
    const pair = TAG_ID_PAIRINGS.find(p => p.tags.includes(tag));
    if (hv(pair)) {
      if (hv(pair.folderId)) {
        joplinFolderIdSet.add(pair.folderId);
      } else {
        Assert.hard(hv(pair.noteId));
        joplinNoteIdSet.add(pair.noteId);
      }
    } else {
      throw new Error(`Tag not found: ${tag}`);
    }
  });

  const result = {
    // Don't save anything if one or more tags are not found -
    // that could result in note getting double-saved or a modifier not getting
    // correctly applied
    joplinNoteIds: Array.from(joplinNoteIdSet),
    joplinFolderIds: Array.from(joplinFolderIdSet),
  };

  return result;
}
