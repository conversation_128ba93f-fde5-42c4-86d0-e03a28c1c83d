import { LinkType, uuidFromOneNoteLink } from "./GetContentControl";

test("uuidFromPageLink correctly pulls a page ID", () => {
  const linkText = `https://onedrive.live.com/view.aspx?resid=4DC6B457EA38D0AC%21396&id=documents&wd=target%28_Todos%2F__Today%2FCurrent.one%7C146934FE-14A8-48BD-AD45-EFB650AD23B3%2FStatus%20of%20Auto%20Parser%7CD08ED124-2F09-6743-8A2F-DAF558902C98%2F%29
onenote:https://d.docs.live.net/4dc6b457ea38d0ac/Documents/Business%20Notebook/_Todos/__Today/Current.one#Status of Auto Parser&section-id={146934FE-14A8-48BD-AD45-EFB650AD23B3}&page-id={D08ED124-2F09-6743-8A2F-DAF558902C98}&end`;

  const resultText = uuidFromOneNoteLink(linkText);
  expect(resultText).toEqual({
    linkType: LinkType.Page,
    uuid: "D08ED124-2F09-6743-8A2F-DAF558902C98".toLowerCase(),
  });
});

test("uuidFromPageLink correctly pulls a section ID", () => {
  const linkText = `https://onedrive.live.com/view.aspx?resid=B453EA553CD6866E%21124&id=documents&wd=target%28zArchived%2F5minrev%2F2MR%20New%20Student%20Member%20Invites.one%7CEC229ACC-25D0-104C-A55A-D74731D85390%2F%29
  onenote:https://d.docs.live.net/b453ea553cd6866e/Documents/Blue%20Sea%20Studios%20Admin/zArchived/5minrev/2MR%20New%20Student%20Member%20Invites.one#section-id={EC229ACC-25D0-104C-A55A-D74731D85390}&end`;

  const resultText = uuidFromOneNoteLink(linkText);
  expect(resultText).toEqual({
    linkType: LinkType.Section,
    uuid: "EC229ACC-25D0-104C-A55A-D74731D85390".toLowerCase(),
  });
});
