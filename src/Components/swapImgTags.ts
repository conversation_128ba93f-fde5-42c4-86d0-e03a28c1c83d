import { Client } from "@microsoft/microsoft-graph-client";
import { getSrcStringForResource } from "./testImageDL";

// Currently unused - OneNote pages are not saving.  Just skipping any with
// images
export async function swapImgTags(
  htmlDocument: Document,
  client: Client,
): Promise<string> {
  // First get sequence of image tags and in-between content
  const images = htmlDocument.getElementsByTagName("img");
  for (let i = 0; i < images.length; ++i) {
    const newImg = htmlDocument.createElement("img");
    console.log(`Downloading image (${i + 1}/${images.length})...`);

    newImg.src = await getSrcStringForResource(client, images[i].src);
    newImg.width = images[i].width;
    newImg.height = images[i].height;
    images[i].insertAdjacentElement("afterend", newImg);
    images[i].remove();
  }

  // htmlDocument.insertBefore(new Node()), images[0])
  // console.log(images);
  return htmlDocument.body.innerHTML;
}
