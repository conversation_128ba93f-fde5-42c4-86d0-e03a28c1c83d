import * as React from "react";
import { useContext, useState } from "react";
import { useForm } from "react-hook-form";
import { MsapiClientContext } from "../GraphService";
import { Assert, hasValue, tee } from "../Tools";

export function GetContentByIdControl() {
  const [loading, setLoading] = useState(false);
  const [innerText, setInnerText] = useState<string | null>(null);
  const { watch, register } = useForm<{ pageID: string }>();
  const client = useContext(MsapiClientContext);

  const isDisabled = loading || watch("pageID", "").length === 0;

  return (
    <form className="m-2">
      <input
        {...register("pageID")}
        className="border"
        style={{ background: "rgb(39, 40, 35)" }}
      />
      <button
        className={`border p-1 m-2 ${
          isDisabled ? "bg-red-700" : "bg-gray-700"
        }`}
        disabled={isDisabled}
        onClick={async event => {
          // still using this code Jan 23 2024, post <PERSON><PERSON><PERSON> transition
          event.preventDefault();
          setInnerText(null);
          setLoading(true);
          try {
            const result = await client
              .api(`/me/onenote/pages/${watch("pageID").trim()}/content`)
              .get();
            setInnerText(result.body.innerHTML);
          } catch (error) {
            if (
              (error as Error).message.includes(
                "The specified resource ID does not exist",
              )
            ) {
              setInnerText("This page ID was not found.");
            }
          }

          setLoading(false);
        }}
      >
        Show page content by ID
      </button>
      {hasValue(innerText) && <p>{innerText}</p>}
    </form>
  );
}

export enum LinkType {
  Page,
  Section,
}

export function GetUuidByPageLinkControl() {
  const [loading, setLoading] = useState(false);
  const [foundGraphId, setFoundGraphId] = useState<{
    graphId: string | null;
    linkType: LinkType;
  }>({ graphId: null, linkType: LinkType.Page });
  const { watch, register } = useForm<{ pageLink: string }>();
  const client = useContext(MsapiClientContext);

  const isDisabled = loading || watch("pageLink", "").length === 0;

  return (
    <form className="m-2">
      <input
        {...register("pageLink")}
        className="border"
        style={{ background: "rgb(39, 40, 35)" }}
      />
      <button
        className={`border p-1 m-2 ${
          isDisabled ? "bg-red-700" : "bg-gray-700"
        }`}
        disabled={isDisabled}
        onClick={async event => {
          event.preventDefault();
          setFoundGraphId({ graphId: null, linkType: LinkType.Page });
          setLoading(true);

          const { uuid, linkType } = uuidFromOneNoteLink(watch("pageLink"));
          Assert.hard(hasValue(uuid));

          const filterClause = tee(
            `contains(links/oneNoteClientUrl/href,'${uuid}')`,
          );
          const apiUrl =
            linkType === LinkType.Page
              ? `/me/onenote/pages`
              : `/me/onenote/sections`;
          // https://stackoverflow.com/a/68295534/152711
          const result = await client.api(apiUrl).filter(filterClause).get();
          const graphId = result.value[0]?.id;
          if (hasValue(graphId)) {
            await navigator.clipboard.writeText(graphId);
          }
          setFoundGraphId({ graphId, linkType });
          setLoading(false);
        }}
      >
        Find page/section ID by OneNote Link
      </button>
      {hasValue(foundGraphId.graphId) && (
        <p>
          Found {LinkType[foundGraphId.linkType]} UUID: {foundGraphId.graphId}{" "}
          <span className="text-green-300">[Copied to clipboard]</span>
        </p>
      )}
    </form>
  );
}

export function uuidFromOneNoteLink(pageLink: string): {
  uuid: string | null;
  linkType: LinkType;
} {
  const pageRegex = /page-id={(.+)}/;
  const pageRegexResult = pageLink.match(pageRegex);
  if (hasValue(pageRegexResult)) {
    return {
      linkType: LinkType.Page,
      uuid: pageRegexResult?.[1].toLowerCase(),
    };
  }

  const sectionRegex = /section-id={(.+)}/;
  const sectionRegexResult = pageLink.match(sectionRegex);
  return {
    linkType: LinkType.Section,
    uuid: sectionRegexResult?.[1].toLowerCase() ?? null,
  };
}
