import { NoteInput } from "../NoteInput";
import { last } from "../Tools";

export function parseRaw<PERSON>son(inputString: string): NoteInput[] {
  let str = replaceInvalidChars(inputString);
  str = trimDownToCore(str);

  // Now we should have something like '{ ... }, { ... }'

  return JSON.parse(`[${str}]`);
}

function trimDownToCore(inputString: string) {
  const iFirstOpen = inputString.indexOf("{");
  const iLastOpen = inputString.lastIndexOf("{");
  const iFirstClose = inputString.indexOf("}");
  const iLastClose = inputString.lastIndexOf("}");

  if (iFirstOpen === -1 || iFirstClose === -1) {
    throw new Error("No objects found in JSON");
  }
  if (iFirstClose < iFirstOpen || iLastOpen > iLastClose) {
    throw new Error("Malformed {} in JSON");
  }

  return inputString.substring(iFirstOpen, iLastClose + 1);
}

function replaceInvalidChars(input: string): string {
  // &nbsp; is not considered valid by JSON.parse
  return input.replace(/\u00A0/g, " ");
}

export function trimChar({ str, ch }: { str: string; ch: string }): string {
  str = str.trim();
  if (str.length === 0) {
    return "";
  }

  if (str[0] === ch) {
    str = str.slice(1).trim();
  }

  if (str.length === 0) {
    return "";
  }

  if (last(str) === ch) {
    str = str.slice(0, str.length - 1);
  }

  return str;
}
