// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import React, { useContext, useEffect, useState } from "react";
import {
  Notebook,
  OnenotePage,
  OnenoteSection,
  SectionGroup,
} from "microsoft-graph";
import { config } from "../Config";
import { MsapiClientContext } from "../GraphService";
import { AuthComponentContext } from "./AuthProvider";
import { PageCollection } from "@microsoft/microsoft-graph-client";
import { filterAndSplit, hasValue, hv, no } from "../Tools";
import { getContentForNotebook, getNotebooks } from "./getNotebooks";
import { useForm } from "react-hook-form";

interface BrowseState {
  notebooks: Notebook[];
  loaded: boolean;
  content: string;
}

export function BrowsePage() {
  const [state, setState] = useState<BrowseState>({
    notebooks: [],
    loaded: false,
    content: "NO CONTENT",
  });

  const [selectedNotebookID, setSelectedNotebookID] = useState<string | null>(
    null,
  );

  const { getAccessToken, user, setError } = useContext(AuthComponentContext);

  useEffect(() => {
    if (!hv(user)) {
      return;
    }

    async function doFetch() {
      try {
        const notebooks: Notebook[] = (await getNotebooks(
          await getAccessToken(config.scopes),
        )) as Notebook[];
        setState({
          loaded: true,
          notebooks,
          content: "LOADED",
        });
      } catch (err) {
        setError("ERROR", JSON.stringify(err));
      }
    }

    void doFetch();
  }, [setError, user, getAccessToken]);

  return (
    <div className="">
      <div>
        <h2>Notebooks</h2>
        <>
          {state.notebooks.map(notebook => {
            return (
              <p key={notebook.id}>
                <button
                  key={notebook.id}
                  className="border p-1"
                  style={
                    notebook.id !== selectedNotebookID
                      ? {}
                      : { backgroundColor: "yellow" }
                  }
                  onClick={async event => {
                    event.preventDefault();
                    setSelectedNotebookID(notebook.id!);
                  }}
                >
                  {notebook.displayName}
                </button>
              </p>
            );
          })}
          <SectionIdInput />
          {hasValue(selectedNotebookID) && (
            <NotebookEntryControl
              key={selectedNotebookID}
              notebookId={selectedNotebookID}
              notebookTitle={
                state.notebooks.find(n => n.id === selectedNotebookID)
                  ?.displayName ?? no()
              }
            />
          )}
        </>
        {state.notebooks.length < 1 && "No notebooks found"}
      </div>
    </div>
  );
}

function NotebookEntryControl({
  notebookId,
  notebookTitle,
}: {
  notebookId: string;
  notebookTitle: string;
}) {
  const [sectionGroups, setSectionGroups] = useState<SectionGroup[]>([]);
  const [sections, setSections] = useState<OnenoteSection[]>([]);

  const client = useContext(MsapiClientContext);

  useEffect(() => {
    async function fetchValues() {
      const {
        sectionGroups: sectionGroupsForNotebook,
        sections: sectionsForNotebook,
      } = await getContentForNotebook(notebookId, client);

      setSectionGroups(sectionGroupsForNotebook.value);
      setSections(sectionsForNotebook.value);
    }

    void fetchValues();
  }, [notebookId, client]);

  return (
    <>
      <Sections sections={sections} parentName={notebookTitle} />
      <SectionGroups sectionGroups={sectionGroups} parentName={notebookTitle} />
    </>
  );
}

function SectionGroupEntryControl({
  id,
  displayName,
}: {
  id: string;
  displayName: string;
}) {
  const [sections, setSections] = useState<OnenoteSection[] | null>(null);
  const [sectionGroups, setSectionGroups] = useState<SectionGroup[] | null>(
    null,
  );
  const client = useContext(MsapiClientContext);

  useEffect(() => {
    async function fetchValues() {
      setSections(null);
      const [sectionsResponse, sectionGroupResponse]: PageCollection[] =
        await Promise.all([
          client.api(`/me/onenote/sectiongroups/${id}/sections`).get(),
          client.api(`/me/onenote/sectiongroups/${id}/sectiongroups`).get(),
        ]);

      setSections(sectionsResponse.value);
      setSectionGroups(sectionGroupResponse.value);
    }

    void fetchValues();
  }, [id, client]);

  return !hasValue(sections) || !hasValue(sectionGroups) ? (
    <p>Loading section group...</p>
  ) : (
    <>
      <Sections parentName={displayName} sections={sections} />
      <SectionGroups parentName={displayName} sectionGroups={sectionGroups} />
    </>
  );
}

function SectionIdInput() {
  const { register, watch } = useForm<{ formSectionId: string }>();
  const [sectionId, setSectionId] = useState<string | null>(null);
  const [displayName, setDisplayName] = useState<string | null>(null);
  const client = useContext(MsapiClientContext);
  return (
    <div>
      <input {...register("formSectionId")} />
      <button
        type="submit"
        onClick={async e => {
          e.preventDefault();
          const sectionResult = await client
            .api(`/me/onenote/sections/${watch("formSectionId")}`)
            .get();
          console.log(sectionResult);
          setSectionId(watch("formSectionId"));
          setDisplayName(sectionResult.displayName);
        }}
      >
        Set Section ID
      </button>

      {hv(sectionId) && hv(displayName) && (
        <SectionEntryControl
          sectionId={sectionId}
          sectionDisplayName={displayName}
        />
      )}
    </div>
  );
}

function SectionEntryControl({
  sectionId,
  sectionDisplayName,
}: {
  sectionId: string;
  sectionDisplayName: string;
}) {
  const [pages, setPages] = useState<OnenotePage[] | null>(null);
  const client = useContext(MsapiClientContext);

  useEffect(() => {
    async function fetchValues() {
      setPages(null);
      const pagesResponse: PageCollection = await client
        .api(`/me/onenote/sections/${sectionId}/pages`)
        .select(["title,id"])
        .top(100)
        .get();

      setPages(pagesResponse.value);
    }

    void fetchValues();
  }, [sectionId, client]);

  return !hasValue(pages) ? (
    <p>Loading section...</p>
  ) : (
    <Pages pages={pages} sectionDisplayName={sectionDisplayName} />
  );
}

export function Pages({
  pages,
  sectionDisplayName,
  termSearched,
}: {
  pages: OnenotePage[];
  sectionDisplayName: string;
  termSearched?: string | null;
}) {
  const { exactMatches, restOfPages } = getExactMatches(pages, termSearched);
  return (
    <>
      <h4>Pages ({sectionDisplayName}):</h4>
      {exactMatches.length > 0 && (
        <>
          <h3 style={{ fontWeight: "bold" }}>Exact matches</h3>
          {exactMatches.map(p => (
            <Page page={p} key={p.id} />
          ))}
        </>
      )}
      <h3 style={{ fontWeight: "bold" }}>Inexact matches</h3>
      {restOfPages.map(p => (
        <Page page={p} key={p.id} />
      ))}
    </>
  );
}

export function matchesSearchTerm(str: string, searchTerm: string) {
  if (searchTerm === "") {
    return false;
  }
  const terms = searchTerm.split(/\s+/);
  return terms.reduce<boolean>((prev, t) => {
    if (!prev) {
      return false;
    }

    return str.toLowerCase().includes(t.toLowerCase());
  }, true);
}

export function getExactMatches(
  pages: OnenotePage[],
  termSearched?: string | null,
): { exactMatches: OnenotePage[]; restOfPages: OnenotePage[] } {
  if (!hv(termSearched) || termSearched.length === 0) {
    return { exactMatches: [], restOfPages: pages };
  }

  const [exactMatches, restOfPages] = filterAndSplit(pages, p =>
    matchesSearchTerm(p.title ?? "", termSearched ?? ""),
  );

  return { exactMatches, restOfPages };
}

function Page({ page: p }: { page: OnenotePage }) {
  const [wasCopied, setWasCopied] = useState(false);

  return (
    <p key={p.id}>
      <button
        className={`border p-1 ${wasCopied && "bg-green-200"}`}
        onClick={async () => {
          await navigator.clipboard.writeText(p.id!);
          setWasCopied(true);
        }}
      >
        Copy ID {wasCopied && "✓"}
      </button>{" "}
      {p.title}
    </p>
  );
}

function Sections({
  sections,
  parentName,
}: {
  sections: OnenoteSection[];
  parentName: string;
}) {
  const [selectedID, setSelectedID] = useState<string | null>(null);
  return (
    <>
      <h3>Sections ({parentName}):</h3>
      {sections.map(v => (
        <button
          key={v.id}
          className="p-1 border"
          style={selectedID !== v.id ? {} : { backgroundColor: "yellow" }}
          onClick={_event => {
            setSelectedID(v.id!);
          }}
        >
          {v.displayName}
        </button>
      ))}
      {hv(selectedID) && (
        <>
          <h4>Selected section ID: {selectedID}</h4>
          <SectionEntryControl
            sectionId={selectedID}
            sectionDisplayName={
              sections.find(s => s.id === selectedID)!.displayName!
            }
          />
        </>
      )}
    </>
  );
}

function SectionGroups({
  sectionGroups,
  parentName,
}: {
  sectionGroups: SectionGroup[];
  parentName: string;
}) {
  const [selectedID, setSelectedID] = useState<string | null>(null);
  return (
    <>
      <h3>Section Groups ({parentName}):</h3>
      {sectionGroups.map(v => {
        return (
          <button
            key={v.id}
            className="p-1 border"
            style={selectedID !== v.id ? {} : { backgroundColor: "yellow" }}
            onClick={_event => {
              setSelectedID(v.id!);
            }}
          >
            {v.displayName}
          </button>
        );
      })}
      {hasValue(selectedID) && (
        <SectionGroupEntryControl
          id={selectedID}
          displayName={
            sectionGroups.find(s => s.id === selectedID)!.displayName!
          }
        />
      )}
    </>
  );
}
