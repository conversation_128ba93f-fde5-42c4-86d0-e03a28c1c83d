// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.

// <ErrorMessageSnippet>
import React from "react";
import { hv } from "../Tools";

interface ErrorMessageProps {
  debug: string;
  message: string;
  backgroundColor?: React.CSSProperties["backgroundColor"];
}

export class ErrorMessage extends React.Component<ErrorMessageProps> {
  render() {
    let debug: any = null;
    if (hv(this.props.debug)) {
      debug = (
        <pre className="alert-pre border bg-light p-2">
          <code>{this.props.debug}</code>
        </pre>
      );
    }
    return (
      <div
        style={{
          backgroundColor: this.props.backgroundColor ?? "red",
          color: "white",
        }}
      >
        <p className="mb-3">{this.props.message}</p>
        {debug}
      </div>
    );
  }
}
// </ErrorMessageSnippet>
