import { Client } from "@microsoft/microsoft-graph-client";
import { <PERSON><PERSON><PERSON> } from "buffer";

export async function getSrcStringForResource(
  client: Client,
  resourceUrl: string,
): Promise<string> {
  const imageData = await downloadImage(client, resourceUrl);
  //             const decoder = new TextDecoder("utf8");
  //             const decoded = decoder.decode(imageData);
  // //            /const b64Data = btoa(decoded);
  //const imageDataUrl = `data:image/png;${imageData}`;
  const b64 = Buffer.from(imageData).toString("base64");

  return `data:image/jpeg;base64,${b64}`;
}

export async function downloadImage(
  client: Client,
  imgSrc: string,
): Promise<Uint8Array> {
  const result: ReadableStream = await client.api(imgSrc).get();
  const reader = result.getReader();

  let data: Uint8Array = new Uint8Array();

  for (
    let readResult = await reader.read();
    !readResult.done;
    readResult = await reader.read()
  ) {
    const value: Uint8Array = readResult.value;
    const prevData = data;
    data = new Uint8Array(data.length + value.length);
    data.set(prevData);
    data.set(value, prevData.length);
  }

  return data;
}

// async function tryProcessImage() {
//   const contentHtml = ""
//   // Works but COULD break down if image alt contains <>?
//   const imgRegex = /<img.+?\/?>/gs;
//   const imgTags = contentHtml.match(imgRegex);
//   console.log(imgTags);
//   // https://stackoverflow.com/a/450117/152711
//   const srcRegex = /src\s*=\s*"(.+?)"/;

//   await resolveInOrder(imgTags ?? [], async t => {
//     const srcText = t.match(srcRegex)?.[1];
//     Assert.hard(hasValue(srcText));
//     console.log(srcText);
//     //      const apiStr = srcText.replace("$value", "content");
//     const imgFile = await client.api(srcText).get();
//     console.log(imgFile);
//     //      console.log(imgFile.text());
//     // const newSrc = fetchStream((imgFile as ReadableStream).getReader());
//     // setImgSrc(newSrc);

//     return srcText;
//   });
// }
// https://stackoverflow.com/a/49428486/152711
// function streamToString (stream: ReadableStreamDefaultReader) {
//   const chunks = [];
//   return new Promise((resolve, reject) => {
//     stream.on('data', (chunk) => chunks.push(Buffer.from(chunk)));
//     stream.on('error', (err) => reject(err));
//     stream.on('end', () => resolve(Buffer.concat(chunks).toString('utf8')));
//   })
// }

// developer.mozilla.org/en-US/docs/Web/API/ReadableStream/getReader
// function fetchStream(reader: ReadableStreamDefaultReader) {
//   let charsReceived = 0;
//   let content = "";
//   let result = "";

//   // read() returns a promise that resolves
//   // when a value has been received
//   reader.read().then(function processText({ done, value }) {
//     // Result objects contain two properties:
//     // done  - true if the stream has already given you all its data.
//     // value - some data. Always undefined when done is true.
//     if (done) {
//       console.log("Stream complete");
//       content = value;
//       return;
//     }

//     // value for fetch streams is a Uint8Array
//     charsReceived += value.length;
//     const chunk = value;

//     result += chunk;

//     // Read some more, and call this function again
//     return reader.read().then(processText);
//   });

//   console.log(charsReceived);
//   console.log(result);
//   console.log(content);
//   return content;
// }
