// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import React, { createContext } from "react";
import { PublicClientApplication } from "@azure/msal-browser";

import { config } from "../Config";
import { getUserDetails } from "../GraphService";
import { hv } from "../Tools";

export interface AuthComponentProps {
  error: any;
  isAuthenticated: boolean;
  user: any;
  login: () => unknown;
  logout: () => unknown;
  getAccessToken: (...args: any[]) => any;
  setError: (...args: any[]) => void;
}

export const AuthComponentContext = createContext<AuthComponentProps>(
  null as unknown as AuthComponentProps,
);

interface AuthProviderState {
  error: any;
  isAuthenticated: boolean;
  user: any;
}

export function withAuthProvider<T extends React.Component<AuthComponentProps>>(
  WrappedComponent: new (props: AuthComponentProps) => T,
): React.ComponentClass {
  return class extends React.Component<
    any,
    AuthProviderState & {
      getAccessToken: (scopes: string[]) => Promise<string>;
    }
  > {
    private publicClientApplication: PublicClientApplication;

    constructor(props: any) {
      super(props);
      this.state = {
        error: null,
        isAuthenticated: false,
        user: {},
        getAccessToken: (scopes: string[]) => this.getAccessToken(scopes),
      };

      // Initialize the MSAL application object
      this.publicClientApplication = new PublicClientApplication({
        auth: {
          clientId: config.appId,
          redirectUri: config.redirectUri,
        },
        cache: {
          cacheLocation: "localStorage",
          storeAuthStateInCookie: true,
        },
      });
    }

    componentDidMount() {
      // If MSAL already has an account, the user
      // is already logged in
      const accounts = this.publicClientApplication.getAllAccounts();

      if (hv(accounts) && accounts.length > 0) {
        // REQUIRED to load authentication info
        void this.getUserProfile();
      }
    }

    render() {
      return (
        <WrappedComponent
          error={this.state.error}
          isAuthenticated={this.state.isAuthenticated}
          user={this.state.user}
          login={() => this.login()}
          logout={() => this.logout()}
          // Pulled getAccessToken out of inline b/c it causes extra useEffect
          // calls further down
          getAccessToken={this.state.getAccessToken}
          setError={(message: string, debug: string) =>
            this.setErrorMessage(message, debug)
          }
          {...this.props}
        />
      );
    }

    async login() {
      try {
        // Login via popup
        await this.publicClientApplication.loginPopup({
          scopes: config.scopes,
          prompt: "select_account",
        });

        // After login, get the user's profile
        await this.getUserProfile();
      } catch (err) {
        this.setState({
          isAuthenticated: false,
          user: {},
          error: this.normalizeError(err as Error),
        });
      }
    }

    logout() {
      void this.publicClientApplication.logout();
    }

    async getAccessToken(scopes: string[]): Promise<string> {
      try {
        const accounts = this.publicClientApplication.getAllAccounts();

        if (accounts.length <= 0) throw new Error("login_required");
        // Get the access token silently
        // If the cache contains a non-expired token, this function
        // will just return the cached token. Otherwise, it will
        // make a request to the Azure OAuth endpoint to get a token
        const silentResult =
          await this.publicClientApplication.acquireTokenSilent({
            scopes,
            account: accounts[0],
          });

        return silentResult.accessToken;
      } catch (err) {
        // If a silent request fails, it may be because the user needs
        // to login or grant consent to one or more of the requested scopes
        if (this.isInteractionRequired(err as Error)) {
          const interactiveResult =
            await this.publicClientApplication.acquireTokenPopup({
              scopes,
            });

          return interactiveResult.accessToken;
        } else {
          throw err;
        }
      }
    }

    // <getUserProfileSnippet>
    async getUserProfile() {
      try {
        const accessToken = await this.getAccessToken(config.scopes);

        if (hv(accessToken)) {
          // Get the user's profile from Graph
          const user = await getUserDetails(accessToken);
          this.setState({
            isAuthenticated: true,
            user: {
              displayName: user.displayName,
              email: user.mail ?? user.userPrincipalName,
              timeZone: user.mailboxSettings.timeZone ?? "UTC",
              timeFormat: user.mailboxSettings.timeFormat,
            },
            error: null,
          });
        }
      } catch (err) {
        this.setState({
          isAuthenticated: false,
          user: {},
          error: this.normalizeError(err as Error),
        });
      }
    }
    // </getUserProfileSnippet>

    setErrorMessage(message: string, debug: string) {
      this.setState({
        error: { message, debug },
      });
    }

    normalizeError(error: string | Error): any {
      let normalizedError = {};
      if (typeof error === "string") {
        const errParts = error.split("|");
        normalizedError =
          errParts.length > 1
            ? { message: errParts[1], debug: errParts[0] }
            : { message: error };
      } else {
        normalizedError = {
          message: error.message,
          debug: JSON.stringify(error),
        };
      }
      return normalizedError;
    }

    isInteractionRequired(error: Error): boolean {
      if (!hv(error.message) || error.message.length <= 0) {
        return false;
      }

      return (
        error.message.indexOf("consent_required") > -1 ||
        error.message.indexOf("interaction_required") > -1 ||
        error.message.indexOf("login_required") > -1 ||
        error.message.indexOf("no_account_in_silent_request") > -1
      );
    }
  };
}
