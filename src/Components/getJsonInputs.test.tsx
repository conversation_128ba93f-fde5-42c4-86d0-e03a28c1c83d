import {
  getRawJsonInputs,
  INVALID_JSON_ERR,
  KEY_ERR,
  STRING_ERR,
  ZERO_ENTRIES_ERR,
} from "./ParseJsonNotesControl";

test("invalid JSON", async () => {
  const jsonStr = "/NOT JSON/";

  const result = getRawJsonInputs(jsonStr);

  expect(result.errorMsg).toEqual(INVALID_JSON_ERR);
});

test("wrong fields", async () => {
  const jsonStr = `[{ "field1": "value" }]`;

  const result = getRawJsonInputs(jsonStr);
  expect(result.errorMsg?.includes(KEY_ERR)).toBeTruthy();
});

test("not a string", async () => {
  const jsonStr = `[{ "noteText": 1 }]`;

  const result = getRawJsonInputs(jsonStr);
  expect(result.errorMsg?.includes(STRING_ERR)).toBeTruthy();
});

test("at least one field needed", async () => {
  const jsonStr = `[{  }]`;

  const result = getRawJsonInputs(jsonStr);
  expect(result.errorMsg).toEqual(ZERO_ENTRIES_ERR);
});

test("success", async () => {
  const jsonStr = `[{"noteText": "here's my note"}, {"highlightText": "hl"}]`;
  const result = getRawJsonInputs(jsonStr);
  expect(result.errorMsg).toBeNull();
  expect(result.rawNoteInputs).toEqual([
    {
      noteText: "here's my note",
    },
    { highlightText: "hl" },
  ]);
});
