import { NoteInput } from "../NoteInput";
import { addDatesToInputs } from "./ParseJsonNotesControl";
import { parseRawJson } from "./parseRawJson";

const jsonCore = `{ "noteText": "test" }, { "noteText": "test2" }`;
const correctJsonFinal = [{ noteText: "test" }, { noteText: "test2" }];

test("normal json - identity", () => {
  const testData = `[${jsonCore}]`;
  const result = parseRawJson(testData);

  expect(result).toEqual(correctJsonFinal);
});

test("no brackets", () => {
  const result = parseRawJson(jsonCore);
  expect(result).toEqual(correctJsonFinal);
});

test("rogue comma etc.", () => {
  const testStrings = [
    `  ,  ${jsonCore} , `,
    ` [ , ${jsonCore} , ] `,
    `  [ , ${jsonCore}   ] `,
    `  [  ${jsonCore}`,
    `  [  ${jsonCore} , `,
    `I'm testing [${jsonCore}] and here's the end`, // trim more
  ];

  const results = testStrings.map(s => parseRawJson(s));
  results.forEach(r => expect(r).toEqual(correctJsonFinal));
});

test("malformed JSON", () => {
  const badStrings = [
    `}{}`,
    `some stuff { } then a rogue open {`,
    `no object here`,
  ];

  badStrings.forEach(s => expect(() => parseRawJson(s)).toThrow());
});

test("add default dates", () => {
  const refDate = "2022-08-21";
  const firstDate = "2022-01-01";
  const secondDate = "2022-02-01";
  const inputs: NoteInput[] = [
    {
      noteText: "gets default",
    },
    {
      noteText: "sets",
      date: firstDate,
    },
    {
      noteText: "gets first",
    },
    {
      noteText: "set second",
      date: secondDate,
    },
    { noteText: "gets second" },
    { noteText: "also gets second" },
  ];

  const expected: NoteInput[] = [
    { ...inputs[0], date: refDate },
    inputs[1],
    { ...inputs[2], date: firstDate },
    inputs[3],
    { ...inputs[4], date: secondDate },
    { ...inputs[5], date: secondDate },
  ];

  expect(addDatesToInputs(inputs, new Date(2022, 7, 21))).toEqual(expected);
});
