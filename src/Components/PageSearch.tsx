import { Client, PageCollection } from "@microsoft/microsoft-graph-client";
import { OnenotePage } from "microsoft-graph";
import React, { useContext, useState } from "react";
import { useForm } from "react-hook-form";
import { AuthComponentContext } from "./AuthProvider";
import { ParseNotesControl } from "./ParseJsonNotesControl";
import {
  GetContentByIdControl,
  GetUuidByPageLinkControl,
} from "./GetContentControl";
import { MsapiClientContext } from "../GraphService";
import { hasValue, P } from "../Tools";
import { HtmlFileUploadControl } from "../ImportHtml/HtmlFileUploadControl";
import { SectionSearch } from "./SectionSearch";
import { Jo<PERSON>lin } from "../Joplin/Joplin";
import { Pages } from "./BrowsePage";
import { config } from "../Config";

export function MainControls() {
  const { isAuthenticated } = useContext(AuthComponentContext);
  if (!isAuthenticated && config.shouldLogInToMsGraph) {
    return null;
  }

  return (
    <>
      <Joplin />
      <SectionSearch />
      <hr className="my-5" />
      <ParseNotesControl />
      <HtmlFileUploadControl />
      {config.shouldLogInToMsGraph && (
        <>
          <hr />
          <PageSearch />
          <GetContentByIdControl />
          <GetUuidByPageLinkControl />
          <TestCreatePageButton />
        </>
      )}
    </>
  );
}

function PageSearch() {
  const { register, watch } = useForm<{ searchTerm: string }>();
  const [loading, setLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<OnenotePage[]>([]);
  const [termSearched, setTermSearched] = useState<string | null>(null);
  const client = useContext(MsapiClientContext);
  const apiUrl = "/me/onenote/pages";
  return (
    <>
      <form>
        <input
          {...register("searchTerm")}
          className="border"
          style={{ background: "rgb(39, 40, 35)" }}
        />
        <button
          className={`border p-1 m-2 ${loading && "bg-red-100"}`}
          disabled={loading}
          onClick={async event => {
            event.preventDefault();
            const invalidCharSearchRegEx =
              /[\[\]?\|\{\}"“”'--–*:$!…./\d’~<>=;_\\@—%‘]/g; // eslint-disable-line no-useless-escape
            const searchTerm = watch("searchTerm")
              .replace(invalidCharSearchRegEx, " ")
              .toLowerCase(); // Lowercase gives different search results!
            if (searchTerm.length < 1) {
              return;
            }
            setLoading(true);
            setSearchResults([]);
            // No numbers, and other chars
            setTermSearched(searchTerm);
            let response: PageCollection = await client
              .api(apiUrl)
              .search(searchTerm)
              .get();
            // Could search titles with:
            // .filter(`contains(tolower(title),'${searchTerm}')`) - sort of works for pages but still a ton it doesn't find

            setSearchResults(response.value);

            while (true) {
              const nextLink: string = Object.entries(response).find(
                e => e[0] === "@odata.nextLink",
              )?.[1] as string;
              if (!hasValue(nextLink)) {
                break;
              }
              response = await client.api(nextLink).get();
              const resultArr = [...response.value];
              setSearchResults(prev => [
                ...prev,
                // Returns dupes sometimes, unclear why
                ...resultArr.filter(
                  r => !prev.map(prevR => prevR.id).includes(r.id),
                ),
              ]);
            }

            setLoading(false);
          }}
        >
          Search Pages
        </button>
      </form>
      <div>
        {searchResults.length === 0 ? null : (
          <Pages
            pages={searchResults}
            sectionDisplayName="Search Result"
            termSearched={termSearched}
          />
        )}
      </div>
    </>
  );
}

function TestCreatePageButton() {
  const client = useContext(MsapiClientContext);
  const [submitting, setSubmitting] = useState(false);
  return (
    <button
      className={`border p-1 ${submitting ? "bg-red-100" : ""}`}
      onClick={async event => {
        event.preventDefault();
        setSubmitting(true);
        await createTestPage(
          { id: "0-4DC6B457EA38D0AC!308119" }, // urgent
          client,
        );
        setSubmitting(false);
      }}
    >
      Create test page
    </button>
  );
}

export async function createTestPage(
  { id: sectionId }: { id: string },
  client: Client,
) {
  // https://docs.microsoft.com/en-us/graph/onenote-create-page
  // Date format example: 2015-07-22T09:00:00-08:00
  const res = await client
    .api(`/me/onenote/sections/${sectionId}/pages`)
    // Not putting space before '---' since likely as not I'll immediately
    // auto-save a note here
    .header("Content-type", "text/html")
    .post(`Test\ntest line 2\n\ntest after skip\n\n\ntwo skip`);

  const pageId = res.id;

  while (true) {
    try {
      await client.api(`/me/onenote/pages/${pageId}/content`).patch([
        {
          target: "body",
          action: "append",
          content: `${P}${pageId}</p>`,
        },
      ]);
      console.log(`Created page: ${pageId}`);
      return pageId;
    } catch (e) {
      if (
        !(e as Error).message.includes(
          "The specified resource ID does not exist",
        )
      ) {
        console.error(e);
        return null;
      }

      const retryMs = 1000;
      // Sometimes it takes a second for the page to be available to patch
      console.log(`waiting to retry: ${retryMs}ms`);
      await new Promise(resolve => setTimeout(() => resolve(null), retryMs));
    }
  }
}
