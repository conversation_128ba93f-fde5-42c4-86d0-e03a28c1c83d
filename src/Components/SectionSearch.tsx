import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { matchesSearchTerm } from "./BrowsePage";
import { format } from "date-fns";
import axios from "axios";
import {
  Jo<PERSON>linFolder,
  createJoplinNoteAtBottom,
  createJoplinUrl,
  fetchAllJoplinFolders,
} from "../Joplin/JoplinShared";
import { hv, pretty } from "../Tools";
import { TagIdPairing } from "../tagIdPairings";

export function SectionSearch() {
  const [sections, setSections] = useState<JoplinFolder[]>([]);
  const { register, watch, setValue } = useForm<{
    searchTerm: string;
    newPageTitle: string;
  }>({ defaultValues: { searchTerm: "", newPageTitle: "" } });
  const [filteredSections, setFilteredSections] = useState<JoplinFolder[]>([]);
  useEffect(() => {
    void fetchAllJoplinFolders().then(folders => setSections(folders));
    // Always run just once
  }, []);

  const uiSearchTerm = watch("searchTerm");
  useEffect(() => {
    const MAX_SECTIONS_TO_DISPLAY = 50;
    const sectionsResult =
      (uiSearchTerm ?? "").length === 0
        ? []
        : sections.filter(
            s =>
              matchesSearchTerm(s.title ?? "", uiSearchTerm) ||
              s.id === uiSearchTerm.trim(),
          );

    setFilteredSections(sectionsResult.slice(0, MAX_SECTIONS_TO_DISPLAY));
  }, [uiSearchTerm, sections]);

  return (
    <div className="my-5">
      <p>
        <input
          {...register("searchTerm")}
          className="border"
          placeholder="Section name or ID..."
          style={{ background: "rgb(39, 40, 35)" }}
        />
      </p>
      {uiSearchTerm.length > 0 && (
        <p className="my-5">
          <input
            {...register("newPageTitle")}
            className="border"
            placeholder="New page title..."
            style={{ background: "rgb(39, 40, 35)" }}
          />
        </p>
      )}
      {filteredSections.map(s => (
        <SectionEntry
          folder={s}
          key={s.id}
          newPageTitle={watch("newPageTitle")}
          handleCreated={() => setValue("newPageTitle", "")}
        />
      ))}
    </div>
  );
}

export function SectionEntry({
  folder,
  newPageTitle,
  handleCreated,
}: {
  folder: JoplinFolder;
  newPageTitle: string;
  handleCreated: () => unknown;
}) {
  const [isLoading, setLoading] = useState(false);
  const [resultMsg, setResultMsg] = useState<string | null>(null);
  const [copyablePageDetails, setCopyablePageDetails] =
    useState<PageDetails | null>(null);
  const [didCopyJson, setDidCopyJson] = useState(false);

  const copyPageDetails = async ({
    title,
    id,
  }: {
    title: string;
    id: string;
  }) => {
    const newItem: TagIdPairing = {
      title,
      date: format(new Date(), "d.MMM.yy"),
      tags: [],
      noteId: id,
    };

    await navigator.clipboard.writeText(`${pretty(newItem)},\n`);
  };
  const isDisabled = newPageTitle.length === 0;
  return (
    <div className="my-4">
      <p>{folder.title}</p>
      <p>
        <button
          disabled={isDisabled}
          onClick={async () => {
            setResultMsg(" (working...)");
            setCopyablePageDetails(null);

            const title = checkAppendTagToTitle(newPageTitle);

            const newNote = await createJoplinNoteAtBottom({
              parent_id: folder.id,
              body: "",
              title,
            });

            const pageDetails = { id: newNote.id, title };

            void copyPageDetails(pageDetails);

            handleCreated();

            // Maybe this isn't necessary but I'm used to this format so will
            // keep using it for now.  With API would be easy to search all
            // and remove these if I wanted.
            await axios.put(await createJoplinUrl(`notes/${newNote.id}`), {
              body: `\n---\n${newNote.id}`,
            });

            setResultMsg(` - created`);
            setCopyablePageDetails(pageDetails);
            setDidCopyJson(true);
            setLoading(false);
          }}
          className={`border p-1 m-1 ${isLoading && "bg-red-700"} ${
            hv(resultMsg) && "bg-green-800"
          } ${isDisabled && "bg-gray-400"}`}
        >
          New page{resultMsg}
        </button>
        {hv(copyablePageDetails) && (
          <button
            onClick={async () => {
              // FF doesn't yet support requesting clipboard write permissions,
              // so we have to do it after a user interaction.
              // https://developer.mozilla.org/en-US/docs/Web/API/Permissions_API
              await copyPageDetails(copyablePageDetails);
              setDidCopyJson(true);
            }}
            className={`border p-1 m-1 ${
              didCopyJson ? "bg-green-800" : "bg-gray-800"
            }`}
          >
            {didCopyJson ? "Copied!" : "Copy JSON"}
          </button>
        )}
        <span> {folder.id}</span>
      </p>
    </div>
  );
}

export function checkAppendTagToTitle(rawTitle: string) {
  const title = rawTitle.trim();
  return (title.includes("[tag]") ? rawTitle : `${title} [tag]`).trim();
}

// DEPRECATED but saving
// async function createPageInSection(
//   { id: sectionId }: { id: string },
//   title: string,
//   client: Client,
// ) {
//   const encodedTitle = encodeTextForHtml(title.trim());
//   // https://docs.microsoft.com/en-us/graph/onenote-create-page
//   // Date format example: 2015-07-22T09:00:00-08:00
//   const dateStr = format(new Date(), "yyyy-M-d");
//   const timeStr = format(new Date(), "hh:mm:ssxxx");
//   const createdDateStr = `${dateStr}T${timeStr}`;
//   console.log(`Creating page with title:`);
//   console.log(title);
//   const res = await client
//     .api(`/me/onenote/sections/${sectionId}/pages`)
//     // Not putting space before '---' since likely as not I'll immediately
//     // auto-save a note here
//     .header("Content-type", "application/xhtml+xml").post(`<!DOCTYPE html>
//     <html>
//       <head>
//         <title>${encodedTitle}</title>
//         <meta name="created" content="${createdDateStr}" />
//       </head>
//       <body>
//         ${P}---</p>
//       </body>
//     </html>`);
//   const pageId = res.id;

//   while (true) {
//     try {
//       await client.api(`/me/onenote/pages/${pageId}/content`).patch([
//         {
//           target: "body",
//           action: "append",
//           content: `${P}${pageId}</p>`,
//         },
//       ]);
//       console.log(`Created page: ${pageId}`);
//       return pageId;
//     } catch (e) {
//       if (
//         !(e as Error).message.includes(
//           "The specified resource ID does not exist",
//         )
//       ) {
//         console.error(e);
//         return null;
//       }

//       const patchRetryMs = 750;
//       // Sometimes it takes a second for the page to be available to patch
//       console.log(`waiting to retry: ${patchRetryMs}ms`);
//       await new Promise(resolve =>
//         setTimeout(() => resolve(null), patchRetryMs),
//       );
//     }
//   }
// }

interface PageDetails {
  title: string;
  id: string;
}
