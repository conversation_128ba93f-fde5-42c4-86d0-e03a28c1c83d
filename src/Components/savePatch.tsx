import * as DateFns from "date-fns";
import { Assert, logger, resolveInOrder } from "../Tools";
import { MdPatch } from "./getPatchForTaggedInput";
import { config } from "../Config";
import { prependJoplinPageContent } from "../Jo<PERSON>lin/Jo<PERSON><PERSON>";
import { createJoplinNoteAtBottom } from "../Jo<PERSON>lin/JoplinShared";

export async function savePatches(patches: MdPatch[], source: NoteSource) {
  return await resolveInOrder(patches, async (p, i) => {
    return await savePatch(p, i, patches.length, source);
  });
}

// Return value: null = success (all tag names ok)
async function savePatch(
  patch: MdPatch,
  patchIndex: number,
  patchCount: number,
  source: NoteSource,
): Promise<void> {
  const content = patch.contentMd;

  Assert.hard(
    [...patch.joplinNoteIds, ...patch.joplinFolderIds].length > 0,
    `no page/section IDs for patch ${content}`,
  );

  console.log(`Running patch ${patchIndex + 1}/${patchCount}:`);

  const truncateMsg = content.length > 500 ? "... [Truncated]" : "";

  console.log(`${content.slice(0, 500)}${truncateMsg}`);

  const pageDateStr = formatPageNoteDate(patch.date, source);

  await resolveInOrder(patch.joplinNoteIds, async id => {
    await prependJoplinPageContent(`${pageDateStr}\n${content}`, id);
    logger.info(`updated joplin page: ${id}`);
  });

  await resolveInOrder(patch.joplinFolderIds, async id => {
    await createJoplinNoteAtBottom({ parent_id: id, body: content });
  });
}

export enum NoteSource {
  Json = "JS",
  OneNote = "ON",
  Outlook = "OL",
  Html = "HT",
  Joplin = "JP",
}

function formatPageNoteDate(date: Date, source: NoteSource) {
  try {
    return `${DateFns.format(date, `d.MMM.yy`)}${getSourceSuffix(source)}`;
  } catch (e) {
    console.error(`date error for: ${date}`);
    throw e;
  }
}

function getSourceSuffix(source: NoteSource) {
  if (config.shouldOutputSourceInDateLine) {
    return ` (${source.toString().toLowerCase()})`;
  } else {
    return "";
  }
}
