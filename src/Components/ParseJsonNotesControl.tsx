import * as DateFns from "date-fns";
import { NoteInput, NoteInputKeys } from "../NoteInput";
import { Assert, dedup, hasValue, hv, pretty } from "../Tools";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { parseRawJson } from "./parseRawJson";
import { NoteSource, savePatches } from "./savePatch";
import {  getMdPatchForTaggedInput } from "./getPatchForTaggedInput";
import { getTaggedInputs } from "../getTags";

export function ParseNotesControl() {
  const { register, watch } = useForm<{ noteJson: string }>();
  const [isLoading, setLoading] = useState(false);
  const [resultData, setResultData] = useState<ResultData>(null);
  const [error, setError] = useState<string | null>(null);

  return (
    <>
      <textarea
        {...register("noteJson")}
        className="border p-1"
        rows={8}
        cols={50}
        placeholder="Raw JSON"
        style={{ background: "rgb(39, 40, 35)" }}
      />
      <p>
        <button
          disabled={isLoading}
          className={`border p-1 my-2 ${isLoading && "bg-red-100"}`}
          onClick={async event => {
            event.preventDefault();
            setError(null);
            setLoading(true);
            setResultData(null);
            const jsonNotesValue = watch("noteJson");
            const { rawNoteInputs, errorMsg } =
              getRawJsonInputs(jsonNotesValue);
            if (hasValue(errorMsg)) {
              setError(errorMsg);
              setLoading(false);
              return;
            }
            const datedInputs = addDatesToInputs(rawNoteInputs);
            const taggedInputResults = datedInputs.map(input =>
              getTaggedInputs(input, { shouldFindAudioTags: true }),
            );

            // Get patches first, in case of any issues
            const patchPairs = taggedInputResults.map((r, i) => ({
              patches: hv(r.invalidTags)
                ? []
                : r.taggedInputs!.map(getMdPatchForTaggedInput),
              input: datedInputs[i],
              errorResult: hv(r.invalidTags)
                ? { invalidTags: r.invalidTags }
                : null,
            }));

            const patchesToSave = patchPairs
              .filter(p => !hv(p.errorResult))
              .flatMap(p => p.patches);

            await savePatches(patchesToSave, NoteSource.Json);

            const skippedNotes = patchPairs
              .filter(p => hv(p.errorResult))
              .map(p => ({
                input: {
                  ...p.input,
                  // Always save skipped date so if I add tags some weeks/months
                  // later, I retain the original approximate date
                  date: p.input.date ?? new Date().toISOString(),
                },
                notFoundTagNames: p.errorResult?.invalidTags,
              }));

            const newResultData = {
              numSaved: patchesToSave.length,
              skippedNotes,
            };

            const { copySkipped } = analyzeResultData(newResultData);
            // FF doesn't yet support requesting clipboard write permissions,
            // so we have to do it after a user interaction.
            // https://developer.mozilla.org/en-US/docs/Web/API/Permissions_API

            await copySkipped();

            setResultData(newResultData);

            const resultString =
              skippedNotes.length > 0
                ? `Skipped: ${skippedNotes.length}`
                : "Handled all notes!";

            console.log(`*** JSON parsing complete. ${resultString}`);

            setLoading(false);
          }}
        >
          Process JSON
        </button>
      </p>
      {hv(error) && `Error: ${error}`}
      <SaveNoteResult resultData={resultData} />
    </>
  );
}

type ResultData = {
  skippedNotes: {
    input: NoteInput;
    notFoundTagNames: string[] | undefined;
  }[];
  numSaved: number;
} | null;

export function addDatesToInputs(
  noteInputs: NoteInput[],
  defaultDate = new Date(),
): NoteInput[] {
  const { datedInputs: result } = noteInputs.reduce<{
    datedInputs: NoteInput[];
    defaultDateStr: string;
  }>(
    (acc, input) => ({
      datedInputs: [
        ...acc.datedInputs,
        { ...input, date: input.date ?? acc.defaultDateStr },
      ],
      defaultDateStr: input.date ?? acc.defaultDateStr,
    }),
    {
      datedInputs: [],
      defaultDateStr: DateFns.format(defaultDate, "yyyy-MM-dd"),
    },
  );
  return result;
}

function analyzeResultData(resultData: ResultData) {
  if (!hv(resultData)) {
    return {
      notFoundTags: [],
      notFoundTagsString: "",
      skippedNotesText: "",
      copySkipped: async () => null,
    };
  }
  const notFoundTags: string[] =
    resultData?.skippedNotes.reduce<string[]>(
      (acc, n) => [...acc, ...(n.notFoundTagNames ?? [])],
      [],
    ) ?? [];

  const notFoundTagsString = dedup(notFoundTags).toSorted().join(", ");
  const skippedNotesText = ` ${resultData.skippedNotes
    .map(n => pretty(n.input))
    .join("\n, ")}`;

  const copySkipped = async () => {
    const clipboardString = `Tags not found: ${notFoundTagsString}\n[\n${skippedNotesText}\n]`;
    // FF doesn't yet support requesting clipboard write permissions,
    // so we have to do it after a user interaction.
    // https://developer.mozilla.org/en-US/docs/Web/API/Permissions_API
    await navigator.clipboard.writeText(clipboardString);
  };

  return { notFoundTags, notFoundTagsString, skippedNotesText, copySkipped };
}

function SaveNoteResult({ resultData }: { resultData: ResultData }) {
  if (!hasValue(resultData)) {
    return null;
  }

  const { notFoundTags, notFoundTagsString, copySkipped, skippedNotesText } =
    analyzeResultData(resultData);

  return (
    <div>
      <p className="font-bold">Results saved: {resultData.numSaved}</p>
      {resultData.skippedNotes.length === 0 ? (
        <p>All results saved!</p>
      ) : (
        <div>
          <button
            onClick={async () => {
              await copySkipped();
            }}
            className={`border-2 p-1 my-2 bg-green-800`}
          >
            Copy Skipped ✓
          </button>
          {notFoundTags.length > 0 && (
            <p className="font-bold">
              <b>Tags not found</b>: {notFoundTagsString}
            </p>
          )}
          <p className="font-bold">Skipped:</p>
          <p>[</p>
          {skippedNotesText.split("\n").map((n, i) => (
            <p className="ml-1" key={i}>
              {n}
            </p>
          ))}
          <p>]</p>
        </div>
      )}
    </div>
  );
}

export const INVALID_JSON_ERR = "Invalid JSON";
export const STRING_ERR = "Value is not a string";
export const KEY_ERR = "Invalid key";
export const ZERO_ENTRIES_ERR = "At least one entry is required";

export function getRawJsonInputs(jsonStr: string): {
  rawNoteInputs: NoteInput[];
  errorMsg: string | null;
} {
  try {
    let errorMsg = null;

    const obj = parseRawJson(jsonStr);
    Assert.hard(Array.isArray(obj), "Expected JSON array");

    (obj as any[]).forEach(el => {
      if (Object.entries(el).length === 0) {
        errorMsg = ZERO_ENTRIES_ERR;
      }
      Object.values(el).forEach(v => {
        if (typeof v !== "string") {
          errorMsg = `${STRING_ERR}: ${v}`;
        }
      });
      Object.keys(el).forEach(k => {
        if (!NoteInputKeys.includes(k)) {
          errorMsg = `${KEY_ERR}: ${k}`;
        }
      });
    });
    if (hasValue(errorMsg)) {
      return { rawNoteInputs: [], errorMsg };
    }
    return { rawNoteInputs: obj, errorMsg: null };
  } catch (err) {
    return { rawNoteInputs: [], errorMsg: INVALID_JSON_ERR };
  }
}
