import { OnenotePage } from "microsoft-graph";
import { getExactMatches, matchesSearchTerm } from "./BrowsePage";

test("Get exact matches", () => {
  const pages: OnenotePage[] = [
    { title: "General Investing" },
    { title: "Invention" },
  ];
  expect(getExactMatches(pages, "gen inv")).toMatchObject({
    exactMatches: [{ title: "General Investing" }],
    restOfPages: [{ title: "Invention" }],
  });
});

test("matchesSearchTerm", () => {
  expect(matchesSearchTerm("social psychology", "soc psych")).toBe(true);
  expect(matchesSearchTerm("social psychology", "soc")).toBe(true);
  expect(matchesSearchTerm("social psychology", "gen psych")).toBe(false);
  expect(matchesSearchTerm("", "anything")).toBe(false);
  expect(matchesSearchTerm("a string", "")).toBe(false);
});
