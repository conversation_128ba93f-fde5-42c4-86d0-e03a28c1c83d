// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
import React, { Component, useContext } from "react";
import {
  BrowserRouter as Router,
  Route,
  redirect,
  Routes,
  Outlet,
} from "react-router-dom";
import {
  AuthComponentContext,
  AuthComponentProps,
  withAuthProvider,
} from "./AuthProvider";
import { NavBar } from "./NavBar";
import { ErrorMessage } from "./ErrorMessage";
import { Welcome } from "./Welcome";
import { getAuthenticatedClient, MsapiClientContext } from "../GraphService";
import { Client } from "@microsoft/microsoft-graph-client";
import { hasValue, hv } from "../Tools";
import { config } from "../Config";

let msApiClient: Client | null = null;

function AppWithoutProvider(authProps?: AuthComponentProps) {
  const { isAuthenticated, getAccessToken } = hv(authProps)
    ? authProps
    : {
        isAuthenticated: false,
        getAccessToken: (_params: any) => "",
      };

  if (config.shouldLogInToMsGraph && msApiClient === null) {
    const token = getAccessToken(config.scopes);
    msApiClient = getAuthenticatedClient(token);
  }

  if (!hasValue(msApiClient) && config.shouldLogInToMsGraph) {
    return <p>"Loading client..."</p>;
  }

  if (!isAuthenticated) {
    redirect("/");
  }

  return (
    <AuthComponentContext.Provider
      value={
        authProps ?? {
          getAccessToken: () => null,
          isAuthenticated: false,
          error: null,
          login: () => null,
          logout: () => null,
          setError: () => null,
          user: null,
        }
      }
    >
      <MsapiClientContext.Provider
        value={msApiClient ?? Client.init({ authProvider: () => null })}
      >
        <Router>
          <Routes>
            <Route path="/" element={<BasicLayout />}>
              <Route index element={<Welcome />} />
            </Route>
          </Routes>
        </Router>
      </MsapiClientContext.Provider>
    </AuthComponentContext.Provider>
  );
}

function BasicLayout() {
  const { isAuthenticated, logout, login, error } =
    useContext(AuthComponentContext);
  return (
    <div className="p-20">
      <NavBar
        isAuthenticated={isAuthenticated}
        authButtonMethod={isAuthenticated ? logout : login}
      />
      <div>
        {!hv(error) ? null : (
          <ErrorMessage message={error.message} debug={error.debug} />
        )}
        {config.shouldLogInToMsGraph && (
          <ErrorMessage
            message=""
            debug="Config: MS Graph Login"
            backgroundColor="blue"
          />
        )}
        <Outlet />
      </div>
    </div>
  );
}

class AppClass extends Component<AuthComponentProps | object> {
  render() {
    return <AppWithoutProvider {...(this.props as any)} />;
  }
}

export const App = config.shouldLogInToMsGraph
  ? withAuthProvider(AppClass as any)
  : AppClass;
