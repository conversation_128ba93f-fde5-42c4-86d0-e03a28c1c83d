// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.

// <NavBarSnippet>
import React from "react";
import { NavLink } from "react-router-dom";
import "@fortawesome/fontawesome-free/css/all.css";
import { config } from "../Config";

interface NavBarProps {
  isAuthenticated: boolean;
  authButtonMethod: any;
}

interface NavBarState {
  isOpen: boolean;
}

export class NavBar extends React.Component<NavBarProps, NavBarState> {
  constructor(props: NavBarProps) {
    super(props);

    this.state = {
      isOpen: false,
    };
  }

  render() {
    if (!config.shouldLogInToMsGraph) {
      return null;
    }
    // Only show Browse nav item if logged in
    let browseLink = null;
    if (this.props.isAuthenticated) {
      browseLink = <NavLink to="/browse">Browse Notebooks</NavLink>;
    }

    return (
      <div>
        <div className="p-2" style={{ width: "100%" }}>
          <NavLink to="/">Home</NavLink>
          {" - "}
          {browseLink}
        </div>
      </div>
    );
  }
}
// </NavBarSnippet>
