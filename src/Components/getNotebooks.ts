import { Client, PageCollection } from "@microsoft/microsoft-graph-client";
import { Notebook } from "microsoft-graph";
import { getAuthenticatedClient } from "../GraphService";
import { Assert, filterAndSplit, hasValue, resolveInOrder } from "../Tools";

const cachedNotebooks: Notebook[] = [];
let contentCacheStarted = false; // Blocking searching?  No - search still broke
const cachedNotebookContents: Map<
  string,
  { sections: PageCollection; sectionGroups: PageCollection }
> = new Map();

const notebooksToCache: string[] = [
  //  disabling caching for now, for less requests on refresh
  //  "0-A0AC2C4AD3A18661!107", // personal
  //  "0-4DC6B457EA38D0AC!396", // business
];

export async function getNotebooks(
  accessToken: string,
): Promise<Notebook[] | null> {
  if (cachedNotebooks.length > 0) {
    return cachedNotebooks;
  }

  if (!hasValue(accessToken)) {
    return null;
  }

  const client = getAuthenticatedClient(accessToken);

  const response: PageCollection = await client
    .api("/me/onenote/notebooks")
    .get();

  const returnedNotebooks = Assert.hasValue(response.value);

  const toCache = returnedNotebooks.filter(n =>
    notebooksToCache.includes(Assert.hasValue(n.id)),
  );

  // Warm up map promises
  if (!contentCacheStarted) {
    contentCacheStarted = true;
    await resolveInOrder(toCache, async notebook => {
      const { sections, sectionGroups } = await fetchNotebookContent(
        notebook.id!,
        client,
      );

      cachedNotebookContents.set(notebook.id!, { sections, sectionGroups });
    });
  }
  const [first, rest] = filterAndSplit(returnedNotebooks, el =>
    notebooksToCache.includes(el.id),
  );

  return [...first, ...rest];
}

export async function getContentForNotebook(
  notebookId: string,
  client: Client,
): Promise<{ sections: PageCollection; sectionGroups: PageCollection }> {
  const cachedContent = cachedNotebookContents.get(notebookId);

  if (hasValue(cachedContent)) {
    return cachedContent;
  }

  const fetchedContent = await fetchNotebookContent(notebookId, client);
  cachedNotebookContents.set(notebookId, fetchedContent);

  return fetchedContent;
}

async function fetchNotebookContent(notebookId: string, client: Client) {
  const [sections, sectionGroups] = await Promise.all([
    client.api(`/me/onenote/notebooks/${notebookId}/sections`).get(),
    client.api(`/me/onenote/notebooks/${notebookId}/sectiongroups`).get(),
  ]);

  return { sections, sectionGroups };
}
