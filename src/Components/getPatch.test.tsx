import {
  getCustomDateFromText,
  getTaggedInputs,
  stripCustomDates,
} from "../getTags";
import {
  JOURNAL_NOTE_ID,
  P1_FOLDER_ID,
  URGENT_FOLDER_ID,
  XIMEI_NOTE_ID,
} from "../Joplin/TestConstants";
import { NoteInput } from "../NoteInput";
import { getMdPatchForTaggedInput, MdPatch } from "./getPatchForTaggedInput";

test("getPatch pulls multiple tags", () => {
  const date = new Date(1);
  const input: NoteInput = {
    noteText: "@@journal .z.ximei something\n_second line_",
    highlightText: undefined,
    sourceTitle: undefined,
    date: date.toISOString(),
  };

  const taggedInputs = getTaggedInputs(input).taggedInputs!;

  const expectedMd: MdPatch = {
    joplinNoteIds: [JOURNAL_NOTE_ID, XIMEI_NOTE_ID],
    joplinFolderIds: [],
    date,
    contentMd: `@@journal .z.ximei something\n_second line_`,
  };

  expect(taggedInputs.map(getMdPatchForTaggedInput)).toEqual([expectedMd]);
});

test("getPatch source + highlight", () => {
  const date = new Date(1);
  const input: NoteInput = {
    noteText: "@@journal .z.p1 something",
    highlightText: "Here is an indubitably memorable quote!",
    sourceTitle: "A Good Book",
    date: date.toISOString(),
  };

  const taggedInputs = getTaggedInputs(input).taggedInputs!;

  const expectedMd: MdPatch = {
    joplinNoteIds: [JOURNAL_NOTE_ID],
    joplinFolderIds: [P1_FOLDER_ID],
    date,
    contentMd: `> Here is an indubitably memorable quote!\n\n@@journal .z.p1 something\n\n---\n_Source: A Good Book_`,
  };

  expect(taggedInputs.map(getMdPatchForTaggedInput)).toEqual([expectedMd]);
});

test("max 2 newlines", () => {
  const date = new Date(1);
  const input: NoteInput = {
    noteText: "@@journal\n\n\n\n\nsome more text\n\n\n\n\n\n\nlast text",
    highlightText: "Just a highlight!",
    sourceTitle: "A Good Book",
    date: date.toISOString(),
  };

  const patches = getTaggedInputs(input).taggedInputs!.map(
    getMdPatchForTaggedInput,
  );

  const expected: MdPatch = {
    joplinNoteIds: [JOURNAL_NOTE_ID],
    joplinFolderIds: [],
    contentMd: `> Just a highlight!\n\n@@journal\n\nsome more text\n\nlast text\n\n---\n_Source: A Good Book_`,
    date,
  };

  expect(patches).toEqual([expected]);
});

test("one-off", () => {
  const date = new Date(1);
  const input: NoteInput = {
    noteText: "@@journal something\n@@o one-off @@u second line",
    date: date.toISOString(),
  };

  const patches = getTaggedInputs(input).taggedInputs!.map(
    getMdPatchForTaggedInput,
  );
  const expected: MdPatch[] = [
    {
      contentMd: `@@journal something\n@@o one-off @@u second line`,
      date,
      joplinNoteIds: [JOURNAL_NOTE_ID],
      joplinFolderIds: [],
    },
    {
      contentMd: `@@o one-off @@u second line`,
      date,
      joplinNoteIds: [],
      joplinFolderIds: [URGENT_FOLDER_ID],
    },
  ];
  expect(patches).toEqual(expected);
});

test("one-off missing tag", () => {
  const date = new Date(1);
  const input: NoteInput = {
    noteText: "something\n@@o one-off @@u second line",
    date: date.toISOString(),
  };

  const result = getTaggedInputs(input);
  // first part is not tagged
  expect(result.invalidTags).toEqual([]);
});

test("getCustomDateFromText", () => {
  const nowDate = new Date(2022, 7, 21);
  const res = getCustomDateFromText(
    "some testing ((2021-02-02)) and done",
    nowDate,
  );
  expect(res).toEqual(new Date(2021, 1, 2));

  const res2 = getCustomDateFromText(
    "last valid 8-1 is same year as reference date ((8-1))",
    nowDate,
  );
  expect(res2).toEqual(new Date(2022, 7, 1));

  const res3 = getCustomDateFromText(
    "should get LAST year because that's last valid December before 'today' ((12-26))",
    nowDate,
  );
  expect(res3).toEqual(new Date(2021, 11, 26));

  expect(() =>
    getCustomDateFromText("far future throws ((2030-1-1))"),
  ).toThrow();

  // Not expecting "((\d" format to occur naturally.
  // Also may have to do lingering [[]]
  // will see if this is too restrictive
  expect(() => getCustomDateFromText("no date (2020-1-1))")).toThrow();

  expect(getCustomDateFromText("none at all")).toBeNull();

  const resText = getCustomDateFromText(
    "old date format ((20.May.22))",
    nowDate,
  );
  expect(resText).toEqual(new Date(2022, 4, 20));

  const res4 = getCustomDateFromText("Another format ((10-1-2019))", nowDate);
  expect(res4).toEqual(new Date(2019, 9, 1));
});

test("sanitizeContentHtml", () => {
  expect(stripCustomDates("Some content ((2022-8-15)) etc")).toEqual(
    "Some content etc",
  );
  expect(stripCustomDates("Some content((2022-8-15))\netc")).toEqual(
    "Some content\netc",
  );

  expect(stripCustomDates("Some content((8-15))\netc")).toEqual(
    "Some content\netc",
  );
});

test("partially found tags", () => {
  const result = getTaggedInputs({
    noteText: `	@@ruby scored 10 point in a row, basketball, tiny so really satisfying.  “Tenacious but tiny”
	 
	“She has a hothead” - paired with big girls and she shoves them.  Has happened in soccer and basketball now
	 
	More than any other kid, gets into a spiral… “use it” they tell her, like channel your emotions/aggression .t. @@SOMEBADTAG @@o (Ruby)`,
  });

  expect(result.invalidTags).toEqual(["somebadtag"]);
});

test("partially found tags 2", () => {
  const res2 = getTaggedInputs({
    noteText: `
@@anotherbadTAG haha wow.  @@chgrow 1400 is just a RELIEF, nothing more really.  But a powerful relief!  Good win too - +10 at the end.  That is DONE now.  @@blitzchess and @@chess.com I feel I can fully retire.  

Chess, So much time alone - done at 1400.  instead learn a guitar song and post it for @@Ximena!  @@o Practice dance, practice what Bruce Lee says!
`,
  });
  expect(res2.invalidTags).toEqual(["anotherbadtag"]);
});
