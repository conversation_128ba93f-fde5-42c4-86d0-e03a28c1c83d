import { parseISO } from "date-fns";
import { sortBy } from "./Comparable";
import { getTaggedInputs } from "./getTags";
import { hv, no } from "./Tools";
import { getMdPatchForTaggedInput } from "./Components/getPatchForTaggedInput";
import { NoteSource, savePatches } from "./Components/savePatch";

export async function processUnfiledNotes(
  unsortedNotes: { date: string; noteText: string; id: string }[],
  handleDelete: (ids: string[]) => Promise<unknown>,
) {
  // chronological
  const notes = unsortedNotes.toSorted(sortBy(n => parseISO(n.date)));
  const allResults = notes.map(note => {
    const { taggedInputs, invalidTags } = getTaggedInputs(note);

    if (hv(invalidTags)) {
      return { patches: null, invalidTags, id: note.id };
    }

    const patches = (taggedInputs ?? no()).map(getMdPatchForTaggedInput);

    return { patches, invalidTags: null, id: note.id };
  });

  const batchSize = 10;
  const batchCount = Math.ceil(allResults.length / batchSize);

  for (let iBatch = 0; iBatch < batchCount; ++iBatch) {
    console.log(`*** Batch #${iBatch + 1} of ${batchCount}`);
    const batchResults = allResults.slice(
      iBatch * batchSize,
      (iBatch + 1) * batchSize,
    );

    await savePatches(
      batchResults.flatMap(r => r.patches ?? []),
      NoteSource.Joplin,
    );

    await handleDelete(batchResults.filter(r => hv(r.patches)).map(r => r.id));
  }

  return allResults;
}

export function getTagsMessage(
  allResults: Awaited<ReturnType<typeof processUnfiledNotes>>,
) {
  const invalidTags = allResults.reduce<string[]>(
    (prev, curr) => [...prev, ...(curr?.invalidTags ?? [])],
    [] as string[],
  );

  if (invalidTags.length === 0) {
    return "All tags handled!";
  }

  const tagSet = new Set(invalidTags);
  return `Invalid Tags: ${Array.from(tagSet).toSorted()}`;
}
