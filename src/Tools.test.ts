import { checkAppendTagToTitle } from "./Components/SectionSearch";
import {
  BR,
  htmlFromContent,
  encodeTextForHtml,
  insertBrInsideLastP,
  P,
} from "./Tools";

test("htmlFromContent", () => {
  expect(htmlFromContent("\n\n content\n\n\nstuff\n\n\n")).toEqual(
    `${P}content<br /></p>${P}stuff</p>`,
  );
  expect(htmlFromContent("  content  \n\n\n  stuff \n2\n", "@ ")).toEqual(
    `${P}@ content</p>${P}@</p>${P}@ stuff</p>${P}@ 2</p>`,
  );
});

test("insertBrInsideLastP", () => {
  const html = `${P}some text</p>`;
  const result = insertBrInsideLastP(html);
  expect(result).toEqual(`${P}some text${BR}</p>`);
});

test("html encode", () => {
  expect(encodeTextForHtml("<> yeah & yes!")).toEqual(
    "&lt;&gt; yeah &amp; yes!",
  );
});

test("append page title", () => {
  expect(checkAppendTagToTitle("  title  ")).toEqual("title [tag]");
  expect(checkAppendTagToTitle("title [tag] and something")).toEqual(
    "title [tag] and something",
  );
});
