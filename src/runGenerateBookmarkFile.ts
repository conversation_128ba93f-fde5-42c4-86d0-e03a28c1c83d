import { writeFile } from "fs/promises";
import { Assert, htmlEscape } from "./Tools";

export async function runGenerateBookmarkFile() {
  const bookmarkJson: { title: string; url: string }[] = JSON.parse(
    process.argv[2],
  );

  console.log(bookmarkJson);

  bookmarkJson.forEach(el => {
    console.log("checking");
    Assert.hasValue(el.title);
    Assert.hasValue(el.url);
  });

  const urlLines = bookmarkJson.map(el => {
    return `<dt><a href="${el.url.trim()}">${htmlEscape(el.title.trim())}</dt>`;
  });

  const fileContent = `<title>Bookmarks</title>
<h1>Bookmarks</h1><dl><p>\n${urlLines.join("\n")}\n</p></dl>`;

  await writeFile(
    `/Users/<USER>/Desktop/new_bookmarks.html`,
    fileContent,
  );
}

void runGenerateBookmarkFile();
