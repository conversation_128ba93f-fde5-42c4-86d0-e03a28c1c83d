import { NoteInput } from "./NoteInput";

export const testData: NoteInput[] = [
  {
    noteText:
      ".n. @@iguse \n\nfor sure its a psychic tax\n\nposting women others dont know... Needs something extra special like acro etc.q. Nyc acro pics were popular. Shooting ppl doing interesting things i like.",
    sourceTitle:
      "The Complete Short Stories Of Ernest <PERSON>: The Finca Vigia Edition",
  },
  {
    noteText:
      "@@autoparse so seems like new strat w books... Evrrything gets a tag. Why n tag it... If n tag then theres no reason to follow up\n\nbasically all iphone.. Bk.. P2k notes get tags now. Then i get alerted if dn havr tag\n\nwhat .d. Abt that hugin pigin or whatever like open source onenote",
    sourceTitle:
      "The Complete Short Stories Of Ernest Hemingway: The Finca Vigia Edition",
  },
  {
    highlightText:
      "waiting for that dazzling brain cell to rise over the horizon.",
    noteText:
      "Creative idea amd noce nonstandard love story\n.....\n\nbook likr fr pt fove.. Solid.. Enjoyed the read.. Noy my new fave tho.\n\n\n.n.c. Finished june",
    sourceTitle: "To Hold Up the Sky",
  },
  {
    noteText:
      "@@autoparse .n. \ncd log all activity to db or jus text file o i can review if anything unexpected",
    sourceTitle:
      "The Complete Short Stories Of Ernest Hemingway: The Finca Vigia Edition",
  },
  {
    noteText:
      "@@urgent .n. Small air quality monitor.q. Make sure lo co two for example... Twenty pct gain is big",
    sourceTitle:
      "The Complete Short Stories Of Ernest Hemingway: The Finca Vigia Edition",
  },
];
