import { v4 } from "uuid";

export const logger = {
  info: (msg: any) => console.info(msg),
  error: (msg: any) => console.error(msg),
};

// Ensures unnecessary checks aren't performed
type NullPart<T> = T & (null | undefined);

type MustBeAmbiguouslyNullable<T> =
  NullPart<T> extends never ? never : NonNullable<T> extends never ? never : T;

export function hasValue<T>(value: T): value is NonNullable<T> {
  return (value as unknown) !== undefined && (value as unknown) !== null;
}

export function no(message?: string): never {
  const assertionMessage = `no() called: ${message ?? "No message"}`;
  console.error(assertionMessage);
  throw new Error(assertionMessage);
}

export const hv = hasValue;

export function hasValueFn<T, A>(
  value: MustBeAmbiguouslyNullable<T>,
  thenFn: (value: NonNullable<T>) => A,
): A | undefined {
  // Undefined matches .? syntax result
  return hasValue(value) ? thenFn(value) : undefined;
}

function assertError(message: string): void {
  logger.error(message);
}

interface AssertHelper {
  hard: (condition: boolean, message?: string) => asserts condition;
  soft: (condition: boolean, message?: string) => void;
  error: (message: string) => void;
  hasValue: <T>(value: T, message?: string) => NonNullable<T>;
}

export const Assert: AssertHelper = {
  hard: (
    condition: boolean,
    message = "Hard Assertion failed",
  ): asserts condition is true => {
    if (!condition) {
      throw new Error(message);
    }
  },
  soft: (condition: boolean, message = "Soft assertion failed"): void => {
    if (!condition) {
      assertError(message);
    }
  },
  error: assertError,
  hasValue: assertHasValue,
};

function assertHasValue<T>(value: T, message?: string): NonNullable<T> {
  Assert.hard(hv(value), message ?? "Assert.hasValue failed");
  return value;
}

export function assertValue<T>(value: T, message?: string): NonNullable<T> {
  Assert.hard(hv(value), message ?? "assertHasValue failed");
  return value;
}

export enum NoteTextType {
  NoneSpecified = 0,
  WrittenNote = 1,
  Highlight = 2,
}

export function tee<A>(a: A) {
  logger.info(a);
  return a;
}

export function teeDir<A>(a: A) {
  console.dir(a);
  return a;
}

export async function resolveInOrder<V, R>(
  inputs: V[],
  callback: (element: V, index: number, previousResults: R[]) => Promise<R>,
): Promise<R[]> {
  return inputs.reduce<Promise<R[]>>(
    async (accArrPromise, val, i) => {
      const prevResults = await accArrPromise;
      const currResult = await callback(val, i, prevResults);
      return [...prevResults, currResult];
    },

    Promise.resolve([]),
  );
}

// Shared with Gmail API project
export interface RawNoteInfo {
  sourceTitle?: string;
  noteText?: string;
  highlightText?: string;
}

export function logify(obj: unknown): void {
  if (obj === null) {
    logger.info(obj);
    return;
  }

  logger.info(pretty(obj));
}

// Includes functions now!
export function pretty(obj: unknown): string {
  return JSON.stringify(
    obj,
    (_key, value) => (typeof value === "function" ? "Function" : value),
    2,
  );
}

interface HasId {
  id: unknown;
}

export function dedupById<T extends HasId>(items: T[]) {
  return dedup(items, el => el.id);
}

export function dedup<T, U>(items: T[], keyGetter?: (value: T) => U) {
  if (keyGetter === undefined) {
    return [...Array.from(new Set(items))];
  } else {
    const reduceResult = items.reduce<{ arr: T[]; keySet: Set<U> }>(
      (acc, item) => {
        const newKey = keyGetter(item);
        if (acc.keySet.has(newKey)) {
          return acc;
        }

        // new item
        acc.keySet.add(newKey);

        return {
          arr: [...acc.arr, item],
          keySet: acc.keySet,
        };
      },

      { arr: [], keySet: new Set<U>() },
    );

    return reduceResult.arr;
  }
}

export function filterAndSplit<T>(array: T[], isValid: (el: T) => boolean) {
  return array.reduce<[T[], T[]]>(
    ([pass, fail], el) =>
      isValid(el) ? [[...pass, el], fail] : [pass, [...fail, el]],
    [[], []],
  );
}

export function last(str: string): string {
  Assert.hard(str.length >= 1, "Can't get last() on empty string");

  return str.slice(-1);
}

// Reasoning for <br /> design:
// 	Autoparse - get rid of <br />, it sucks in ON

// 	Hyper+h doesn't work to delete line
// 	Indenting causes table
export const P = `<p style="margin-top:0pt;margin-bottom:0pt">`;
export const BR = `<br />`;

export function htmlFromContent(str: string, linePrefix?: string) {
  Assert.hard(linePrefix !== "");
  const cleanedLines = cleanRawContent(str);

  // Save individual P.  If I save whole patch as one big P then it's
  // clunkier to work with, e.g. checkbox in ON can only be placed on the
  // very first line of the patch
  return cleanedLines
    .map((line, i, arr) => {
      if (line === "" && !hasValue(linePrefix)) {
        return ""; // blank line is handled with BR in preceding line
      }
      // If next line is blank, insert BR to represent this
      const needsBr =
        !hasValue(linePrefix) && i + 1 < arr.length && arr[i + 1] === "";
      return `${P}${`${linePrefix ?? ""}${line}`.trim()}${
        needsBr ? BR : ""
      }</p>`;
    })
    .join("");
}

function cleanRawContent(str: string): string[] {
  return str
    .trim()
    .split("\n")
    .map(l => l.trim())
    .join("\n")
    .replace(/\n\n+/g, "\n\n") // max two new lines
    .split("\n");
}

export function mdFromContent(str: string, linePrefix = "") {
  const cleanedLines = cleanRawContent(str);
  return cleanedLines.map(l => `${linePrefix}${l}`).join("\n");
}

export function insertBrInsideLastP(html: string) {
  const lastTagRegex = /(.+)<(?!.*<)/gm;
  const [, beforeLastClosing] = lastTagRegex.exec(html) ?? no();
  return `${beforeLastClosing}${BR}</p>`;
}

export const MIN_DATE = new Date(0);

// Seems ok.  From: https://stackoverflow.com/a/14130027/152711
export function encodeTextForHtml(text: string) {
  return text
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;");
}

export enum DeleteRoute {
  StickyNote = "messages",
  OneNotePage = "onenote/pages",
}

export function getDeleteRequest(noteId: string, routeString: DeleteRoute) {
  return {
    id: v4(), // ID seems not to matter at all
    // Results in intermediate "localhost:3000/me/..." but that seems ok
    request: new Request(`/me/${routeString}/${noteId}`, {
      method: "DELETE",
    }),
  };
}

// https://stackoverflow.com/a/77383580/152711
export function decodeBase64ToUtf8(base64EncodedString: string) {
  try {
    return new TextDecoder().decode(
      Uint8Array.from(
        window.atob(base64EncodedString),
        m => m.codePointAt(0) ?? no(),
      ),
    );
  } catch (error) {
    console.error("Parse error for base 64 string:");
    console.log(base64EncodedString);
    throw error;
  }
}

export type URLParamsInput = ConstructorParameters<typeof URLSearchParams>[0];

export function makeRegexGlobal(sourceRegex: RegExp) {
  if (sourceRegex.global) {
    return new RegExp(sourceRegex);
  }

  return new RegExp(sourceRegex, `${sourceRegex.flags}g`);
}

export function htmlEscape(str: string) {
  return str
    .replace(/&/g, "&amp;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#39;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;");
}

export function downloadFile(
  content: BlobPart,
  filename: string,
  mimeType: string,
) {
  const blob = new Blob([content], { type: mimeType });
  const element = document.createElement("a");
  element.href = URL.createObjectURL(blob);
  element.download = filename;
  document.body.appendChild(element); // Required for this to work in FireFox
  element.click();
}

export function getSourceFooter(sourceTitle: string) {
  return `---\n_Source: ${sourceTitle}_`;
}
