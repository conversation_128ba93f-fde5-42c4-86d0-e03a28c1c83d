import { sortBy } from "./Comparable";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  fetch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ist,
  jNoteFieldsObj,
} from "./Jo<PERSON><PERSON>/JoplinShared";

// FOR REFERENCE, leaving for now
async function runShowLargestNotes() {
  const notes: <PERSON><PERSON><PERSON><PERSON><PERSON>[] = await fetchLong<PERSON><PERSON>linList(
    "notes",
    jNoteFieldsObj,
  );

  const largest = notes.toSorted(sortBy(n => n.body.length));
  console.log(largest.slice(-100).map(n => n.title));
}

void runShowLargestNotes();
