import {
  BatchRequestContent,
  BatchRequestStep,
  Client,
} from "@microsoft/microsoft-graph-client";
import { decodeBase64ToUtf8, hv } from "./Tools";

const maxBatchSize = 20;

export async function submitBatchRequests<T>(
  requestSteps: BatchRequestStep[],
  client: Client,
  initialCachedResponses: unknown[] = [],
  handleCumulativeData?: (cumulativeData: any[]) => Promise<unknown>,
): Promise<T[]> {
  const responses: any[] = initialCachedResponses;

  for (
    let i = initialCachedResponses.length;
    i < requestSteps.length;
    i += maxBatchSize
  ) {
    console.log(
      `Batching OneNote page content fetch, (${i}/${requestSteps.length})...`,
    );
    const batchRequestContent = new BatchRequestContent(
      requestSteps.slice(i, i + maxBatchSize),
    );
    const content = await batchRequestContent.getContent();

    const postResult = await client.api("/$batch").post(content);
    // POST the batch request content to the /$batch endpoint

    postResult.responses.forEach(
      (response: { body: string | undefined; id: string }) => {
        if (hv(response.body)) {
          const decodedBody = decodeBase64ToUtf8(response.body);
          if ((decodedBody as string).includes(`"innerError":`)) {
            throw new Error(
              `Error fetching page body.  ID: ${response.id} body: ${decodedBody}`,
            );
          }
        }
      },
    );

    responses.push(...postResult.responses);

    console.log("Successful batch responses so far:");
    console.log(responses);

    await handleCumulativeData?.(responses);
  }

  return responses;
}
