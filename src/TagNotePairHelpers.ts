import { getSortedRawLowerTagsForText } from "./getTags";
import { diaryIndicators } from "./Joplin/Constants";
import { TAG_ID_PAIRINGS } from "./PairingHelpers";
import { dedup } from "./Tools";

export function prepRawTags(rawTags: string[]): string[] {
  return dedup(rawTags.map(t => t.trim().toLowerCase()));
}

const universalIndicators = ["a", "all"];

// Universal applies to ALL notes within an article
export function isUniversalIndicator(tagName: string) {
  return universalIndicators.includes(tagName.trim().toLowerCase());
}

export function isDiaryIndicator(tagName: string) {
  return diaryIndicators.includes(tagName.trim().toLowerCase());
}

export function hasDiaryIndicator(text: string) {
  const rawTags = getSortedRawLowerTagsForText(text, {
    shouldAllowUniversalWithOneOff: true,
  });

  return diaryIndicators.some(s => rawTags.includes(s.toLowerCase()));
}

// indicates this para should be additionally tagged apart from whole
// Supported in all but OneNote Unfiled (not easy to separate the HTML)
export function isOneOffIndicator(tagName: string): boolean {
  return ["o", "oneoff"].includes(tagName.trim().toLowerCase());
}

// I think this makes more sense as a title than "isnotfound" because "invalid"
// more clearly indicates it's also not "a" "o" etc.
export function isFoundTag(tag: string) {
  const everyTag = TAG_ID_PAIRINGS.flatMap(p => p.tags);
  const isFound =
    isUniversalIndicator(tag) ||
    isOneOffIndicator(tag) ||
    everyTag.findIndex(
      t => t.trim().toLowerCase() === tag.trim().toLowerCase(),
    ) >= 0;

  return isFound;
}

export function includesUniversalTag(tagNames: string[]): boolean {
  return tagNames.some(n => isUniversalIndicator(n));
}
