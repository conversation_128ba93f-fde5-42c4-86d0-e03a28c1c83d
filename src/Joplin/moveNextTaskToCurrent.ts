import { navigateToNote } from "./commandLineTools";
import { J_CURRENT_FOLDER_ID, J_DONE_FOLDER_ID } from "./Constants";
import {
  Jo<PERSON><PERSON>Note,
  fetchAllJoplinFolders,
  fetchJoplinNotesInFolder,
  fetch<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ist,
  jNoteFieldsObj,
  moveNotesToFolder,
  patchJoplinNote,
  toSortedNotesInFolder,
} from "./JoplinShared";

export async function moveNextTaskToCurrent() {
  // First move all current to Done
  const currentFolderNotes =
    await fetchJoplinNotesInFolder(J_CURRENT_FOLDER_ID);

  await moveNotesToFolder(currentFolderNotes, J_DONE_FOLDER_ID);

  // Make sure title ordering is identical
  const allFolders = await fetchAllJoplinFolders({ order_by: "title" });

  const taskSourceFolder = allFolders.filter(
    f => f.parent_id === J_CURRENT_FOLDER_ID,
  )[0];

  const sourceNotes: Jo<PERSON>linNote[] = toSortedNotesInFolder(
    await fetchLongJoplinList(`folders/${taskSourceFolder.id}/notes`, {
      ...jNoteFieldsObj,
    }),
  ).toReversed();

  if (sourceNotes.length === 0) {
    console.log("No notes in folder");
    return;
  }

  const { list: notesToMove } = sourceNotes.reduce(
    (acc, n, i) => {
      if (acc.isDone) {
        return acc;
      }

      if (i === 0 || /^\.\. /.test(n.title)) {
        return { list: [...acc.list, n], isDone: false };
      }

      return { list: acc.list, isDone: true };
    },
    { list: [] as JoplinNote[], isDone: false },
  );

  await Promise.all(
    notesToMove.map(n =>
      patchJoplinNote(n.id, { parent_id: J_CURRENT_FOLDER_ID }),
    ),
  );

  navigateToNote(notesToMove[0].id);
}
