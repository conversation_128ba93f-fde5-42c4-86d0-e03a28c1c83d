import {
  fetchJoplinNotesInFolder,
  fetchOrderedSourceNotesFromMarkdownLinkArg,
  patchJoplinNote,
  toSortedNotesInFolder,
} from "./JoplinShared";

// Still unused - I'm returning to the attempt at reordering after creation (runSetNewNoteOrder.ts)
async function runSetOrdersForFolder() {
  const selectedNotes = await fetchOrderedSourceNotesFromMarkdownLinkArg();

  const allFolderNotes = toSortedNotesInFolder(
    await fetchJoplinNotesInFolder(selectedNotes[0].parent_id),
  );

  // Move new note just below previous lowest note was
  await Promise.all(
    allFolderNotes.map(async (n, i) => {
      await patchJoplinNote(n.id, {
        order: i + 1,
      });
    }),
  );
}

void runSetOrdersForFolder();
