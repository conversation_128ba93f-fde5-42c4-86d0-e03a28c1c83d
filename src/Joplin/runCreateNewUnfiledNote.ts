import { format } from "date-fns";
import { navigateToNote } from "./commandLineTools";
import { J_UNFILED_FOLDER_ID } from "./Constants";
import { createJoplinNoteAtTop } from "./JoplinShared";

async function createNewUnfiledNote() {
  return await createJoplinNoteAtTop({
    title: format(new Date(), "MMM d HH:mm"),
    body: "",
    parent_id: J_UNFILED_FOLDER_ID,
  });
}

const note = await createNewUnfiledNote();
navigateToNote(note.id);
