import axios from "axios";
import { createJoplinUrl, fetchNotesInFolder } from "./JoplinShared";
import { sortByDesc } from "../Comparable";

const allFolderKeys = [
  "id",
  "title",
  "created_time",
  "updated_time",
  "user_created_time",
  "user_updated_time",
  "encryption_cipher_text",
  "encryption_applied",
  "parent_id",
  "is_shared",
  "share_id",
  "master_key_id",
  "icon",
  "user_data",
  "deleted_time",
];

// Still unused - I'm returning to the attempt at reordering after creation (runSetNewNoteOrder.ts)
async function runFullLogFolder() {
  const testFolderId = "f51129962afe4a86ae6990e29aa72b68";

  const _folderResponse = await axios.get(
    await createJoplinUrl(`folders/${testFolderId}`, {
      fields: allFolderKeys.join(","),
    }),
  );

  const pages = await fetchNotesInFolder(testFolderId);

  console.log(pages.toSorted(sortByDesc(p => p.order)));
}

void runFullLogFolder();
