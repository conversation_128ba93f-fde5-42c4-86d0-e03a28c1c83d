import { pretty } from "../Tools";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  fetch<PERSON>ong<PERSON><PERSON><PERSON>List,
  jF<PERSON>erFieldsObj,
} from "./JoplinShared";

async function runListFolders() {
  const folders = await fetchLongJoplinList<JoplinFolder>(
    "folders",
    jFolderFieldsObj,
  );

  console.log(pretty(folders.map(f => `${f.title} [[${f.id}]]`)));
}

void runListFolders();
