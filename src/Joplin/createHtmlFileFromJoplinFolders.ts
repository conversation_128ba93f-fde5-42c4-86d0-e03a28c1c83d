import { format } from "date-fns";
import { downloadFile, htmlEscape, hv, resolveInOrder } from "../Tools";
import { cachedJoplinHtmlFolderList } from "./cachedJoplinHtmlBookmarkFolderList";
import { JoplinFolder, fetchAllJoplinFolders } from "./JoplinShared";
import { sortBy } from "../Comparable";

export async function createBookmarkHtmlFileFromJoplinFolders() {
  const rawJoplinFolders = (await fetchAllJoplinFolders()).toSorted(
    sortBy(f => f.title),
  );

  const newBookmarkFolders = rawJoplinFolders.filter(getIsNewBookmark);

  if (newBookmarkFolders.length === 0) {
    console.log("Nothing new!");
    return;
  }

  console.log("*** all folders TO LOG in cachedJoplinHtmlFolderList:");
  console.log(rawJoplinFolders);

  console.log("New bookmarks:");
  newBookmarkFolders.forEach(f => console.log(f.title));

  const urlLines = await resolveInOrder(newBookmarkFolders, async folder => {
    const url = createJoplinFolderLinkFromId(folder.id);

    return `<dt><a href="${htmlEscape(url.trim())}">${htmlEscape(
      folder.title.trim(),
    )}</dt>`;
  });

  const fileContent = `<title>Bookmarks</title>
<h1>Bookmarks</h1><dl><p>\n${urlLines.join("\n")}\n</p></dl>`;

  downloadFile(
    fileContent,
    `Joplin folders ${format(new Date(), "yyyy-MM-dd")} (${
      newBookmarkFolders.length
    } bookmark${newBookmarkFolders.length > 1 ? "s" : ""}).html`,
    "text/html",
  );
}

function createJoplinFolderLinkFromId(folderId: string) {
  return `joplin://x-callback-url/openFolder?id=${folderId}`;
}

function getIsNewBookmark(fetchedFolder: JoplinFolder) {
  const cachedFolder = cachedJoplinHtmlFolderList.find(
    f => f.id === fetchedFolder.id,
  );
  const isNew = !hv(cachedFolder);
  if (isNew) {
    return true;
  }

  const isRenamed = cachedFolder.title !== fetchedFolder.title;
  if (isRenamed) {
    return true;
  }

  return false;
}
