import { J_CURRENT_FOLDER_ID, J_UNFILED_FOLDER_ID } from "./Constants";
import { fetchNotesInFolder, moveNotesToFolder } from "./JoplinShared";

async function moveMobileCurrentToUnfiled() {
  const currentNotes: { id: string; source_application: string }[] =
    (await fetchNotesInFolder(J_CURRENT_FOLDER_ID, {
      fields: "id,source_application",
    })) as any[];

  const mobileNotes = currentNotes.filter(
    n => n.source_application === "net.cozic.joplin-mobile",
  );

  await moveNotesToFolder(mobileNotes, J_UNFILED_FOLDER_ID);
}

void moveMobileCurrentToUnfiled();
