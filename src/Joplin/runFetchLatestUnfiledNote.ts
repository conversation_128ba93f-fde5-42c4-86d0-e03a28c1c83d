import { J_UNFILED_FOLDER_ID } from "./Constants";
import { fetchNotesInFolder, toSortedNotesInFolder } from "./JoplinShared";

async function fetchLatestUnfiledNoteOrEmptyFolderUrl() {
  const notes = await fetchNotesInFolder(J_UNFILED_FOLDER_ID);

  if (notes.length === 0) {
    // Just link to folder - this is used for linking in Maestro
    return `joplin://x-callback-url/openFolder?id=${J_UNFILED_FOLDER_ID}`;
  }

  const noteId = toSortedNotesInFolder(notes).slice(-1)[0].id;

  return `joplin://x-callback-url/openNote?id=${noteId}`;
}

void fetchLatestUnfiledNoteOrEmptyFolderUrl().then(url => console.log(url));
