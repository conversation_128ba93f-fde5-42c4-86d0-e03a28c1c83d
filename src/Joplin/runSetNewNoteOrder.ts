import { sortBy } from "../Comparable";
import {
  fetchNotesInFolder,
  fetchOrderedSourceNotesFromMarkdownLinkArg,
  patchJoplinNote,
  toSortedNotesInFolder,
} from "./JoplinShared";

async function runSetNewNoteBelowSelectedOrder() {
  const selectedNote = (await fetchOrderedSourceNotesFromMarkdownLinkArg())[0];
  const rawFolderNotes = await fetchNotesInFolder(selectedNote.parent_id);

  // 0-based
  const newNote = rawFolderNotes.toSorted(sortBy(n => -n.user_created_time))[0];

  const sortedPreexistingNotes = toSortedNotesInFolder(rawFolderNotes).filter(
    n => n.id !== newNote.id,
  );

  const iSelectedNote = sortedPreexistingNotes.findIndex(
    n => n.id === selectedNote.id,
  );

  let newNoteOrderValue;

  const isFolderFullyOrdered = sortedPreexistingNotes.every(n => n.order > 0);

  if (isFolderFullyOrdered) {
    newNoteOrderValue =
      iSelectedNote === 0
        ? selectedNote.order / 2
        : (selectedNote.order +
            sortedPreexistingNotes[iSelectedNote - 1].order) /
          2;
  } else {
    // Move new note just below previous lowest note
    // pages will jump even if I use resolveInOrder...
    await Promise.all(
      sortedPreexistingNotes.map(async (n, i) => {
        const order = i >= iSelectedNote ? i + 2 : i + 1;
        await patchJoplinNote(n.id, {
          order,
        });
      }),
    );

    newNoteOrderValue = iSelectedNote + 1;
  }

  await patchJoplinNote(newNote.id, { order: newNoteOrderValue });
}

void runSetNewNoteBelowSelectedOrder();
