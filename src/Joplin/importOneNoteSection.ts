import { Client } from "@microsoft/microsoft-graph-client";
import {
  InProgressSectionData,
  fetchOneNotePagesInOrderForSection,
} from "./OneNoteExport";
import { getMdFromHtml } from "./getMdFromHtml";
import {
  Jo<PERSON><PERSON>Folder,
  createJ<PERSON>linNote,
  createJoplinUrl,
  fetchJoplinNote,
  fetchNotesInFolder,
  getTitleFromBody,
  patchJoplinNote,
} from "./JoplinShared";
import axios from "axios";
import { Assert, hv, no, pretty, resolveInOrder } from "../Tools";
import { OnenoteSectionCache } from "../sectionCache";
import {
  J_CURRENT_FOLDER_ID,
  J_IMPORTED_SECTIONS_NOTE_ID,
  J_IN_PROGRESS_SECTION_DB_NOTE_ID,
} from "./Constants";
import { IMPORTED_ONENOTE_PAGE_IDS, TAG_ID_PAIRINGS } from "../PairingHelpers";

export async function importOneNoteSection(
  client: Client,
  sectionId: string,
): Promise<JoplinFolder> {
  const importedOnenoteSectionCache = await fetchImportedOnenoteSectionCache();
  const foundAlreadyImportedSection = importedOnenoteSectionCache.find(
    el => el.id === sectionId,
  );
  if (hv(foundAlreadyImportedSection)) {
    throw new Error(
      `Already imported: ${foundAlreadyImportedSection.displayName}`,
    );
  }

  const inProgressSectionData = await fetchInProgressSectionData();
  console.log("Starting in-progress data:");
  console.log(inProgressSectionData);

  if (
    hv(inProgressSectionData.sectionId) &&
    inProgressSectionData.sectionId !== sectionId
  ) {
    throw new Error(
      `Another section is already in progress: ${inProgressSectionData.sectionId}`,
    );
  }

  console.log(`importing: ${sectionId}`);
  const sectionResult = await client
    .api(`/me/onenote/sections/${sectionId}`)
    .get();

  const sectionDisplayName: string = sectionResult.displayName;
  console.log(sectionDisplayName);

  const allPages = await fetchOneNotePagesInOrderForSection(
    sectionId,
    client,
    inProgressSectionData,
    async (newData: InProgressSectionData) => {
      await updateInProgressSectionData(newData);
    },
  );

  const newFolderUrl = await createJoplinUrl("folders");
  const newTitle = sectionResult.displayName.trim();
  const newFolder = await axios.post(newFolderUrl, {
    title: newTitle,
    parent_id: J_CURRENT_FOLDER_ID,
  });

  const indentPrefix = ".. ";

  let notebookToDeleteId: string | undefined = undefined;

  await resolveInOrder(allPages, async p => {
    let titlePrefix = "";
    for (let i = 0; i < p.level; ++i) {
      titlePrefix += indentPrefix;
    }
    // Check if it was already imported as a single page
    const hasAutoparsePage = IMPORTED_ONENOTE_PAGE_IDS.includes(p.id);

    let bodyMd: string;
    let title: string;
    if (hasAutoparsePage) {
      const pairing = TAG_ID_PAIRINGS.findLast(
        el => el.oldOnenotePageId === p.id,
      );
      const note = await fetchJoplinNote(
        pairing?.noteId ?? no(`no match for ${p.title}`),
      );
      bodyMd = note.body;
      title = `${titlePrefix}${note.title}`;

      notebookToDeleteId = note.parent_id;

      await axios.delete(await createJoplinUrl(`notes/${note.id}`));
    } else {
      bodyMd = getMdFromHtml(p.htmlContent);
      const trimmedTitle = p.title.trim();
      const titleOrBody =
        trimmedTitle.length > 0 ? trimmedTitle : getTitleFromBody(bodyMd);
      const sanitizedTitle = titleOrBody.replace(/^--+/, "===");
      title = `${titlePrefix}${sanitizedTitle}`;
    }

    if (bodyMd.trim() === "" && p.title.trim() === "") {
      return null; // Ignore completely blank pages
    }

    return await createJoplinNote({
      parent_id: newFolder.data.id,
      body: bodyMd,
      title,
      user_created_time: p.createdDateTime.getTime(),
    });
  });

  if (hv(notebookToDeleteId)) {
    const notes = await fetchNotesInFolder(notebookToDeleteId);
    if (notes.length > 0) {
      console.warn(
        `UNEXPECTED - folder not empty, skipping delete. ID: ${notebookToDeleteId}`,
      );
    } else {
      const deleteUrl = await createJoplinUrl(`folders/${notebookToDeleteId}`);
      await axios.delete(deleteUrl);
      console.log(`Deleted empty folder: ${notebookToDeleteId}`);
    }
  }

  if (!sectionDisplayName.startsWith("[JOP")) {
    // Now rename
    await client.api(`/me/onenote/sections/${sectionId}`).patch({
      displayName: `[JOP] ${sectionDisplayName.trim()}`.slice(0, 50), // 50 is max
    });
  }

  const newImport: OnenoteSectionCache = {
    id: sectionId,
    displayName: sectionDisplayName,
  };
  const finalSectionCache = [...importedOnenoteSectionCache, newImport];
  const dbPageBody = pretty(finalSectionCache);
  console.log("updating 'db' in Joplin");
  await patchJoplinNote(J_IMPORTED_SECTIONS_NOTE_ID, {
    body: dbPageBody,
  });

  await clearInProgressSectionData();

  console.log(`DONE importing: ${newTitle}`);
  return newFolder.data;
}

export async function clearInProgressSectionData() {
  // Reset - nothing in progress now
  await updateInProgressSectionData({
    sectionId: null,
    nextMetadataLink: null,
    cachedContent: [],
    cachedMetadata: [],
  });
}

async function fetchImportedOnenoteSectionCache(): Promise<
  OnenoteSectionCache[]
> {
  const importedSectionDbNote = await fetchJoplinNote(
    J_IMPORTED_SECTIONS_NOTE_ID,
  );
  return JSON.parse(importedSectionDbNote.body);
}

export async function fetchInProgressSectionData(): Promise<InProgressSectionData> {
  const inProgressSectionDbNote = await fetchJoplinNote(
    J_IN_PROGRESS_SECTION_DB_NOTE_ID,
  );

  if (inProgressSectionDbNote.body.trim().length === 0) {
    return {
      sectionId: null,
      cachedMetadata: [],
      cachedContent: [],
      nextMetadataLink: null,
    };
  }

  const inProgressObj: InProgressSectionData = JSON.parse(
    inProgressSectionDbNote.body,
  );

  Assert.hard(
    hv(inProgressObj.cachedMetadata) &&
      hv(inProgressObj.cachedContent) &&
      inProgressObj.sectionId !== undefined &&
      inProgressObj.nextMetadataLink !== undefined, // may be null
    "Unexpected DB format for in-progress section import",
  );

  return inProgressObj;
}

async function updateInProgressSectionData(
  data: InProgressSectionData,
): Promise<void> {
  await patchJoplinNote(J_IN_PROGRESS_SECTION_DB_NOTE_ID, {
    body: pretty(data),
  });
  if (hv(data.sectionId)) {
    console.log(`Updated in-progress data:`);
    console.log(data);
  } else {
    console.log("Cleared in-progress data");
  }
}
