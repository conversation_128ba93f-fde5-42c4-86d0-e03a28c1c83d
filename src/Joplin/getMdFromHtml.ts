import { NodeHtmlMarkdown, NodeHtmlMarkdownOptions } from "node-html-markdown";
import { Assert, hv, makeRegexGlobal } from "../Tools";

const htmlLogStr = "NOT USING HTML LOG STRING";
const brCode = "새새새";
const tildeCode = "티티티";
const periodCode = "피피피";

// Note that <PERSON><PERSON><PERSON> can import directly from HTML!  But, I likely want to
// set parameters and make it look precisely good, since it will run thousands
// of times minimum...

export function getMdFromHtml(rawHtmlContent: string) {
  // Indent example
  // if (htmlContent.includes("Cash in bank ")) {
  // file example
  if (rawHtmlContent.includes(htmlLogStr)) {
    console.log("totally raw:");
    console.log(rawHtmlContent);
  }

  // for example, page "Biggest - just be relaxed" in Specific Biz Goals
  const processedHtml = preprocessHtml(rawHtmlContent);

  if (rawHtmlContent.includes(htmlLogStr)) {
    console.log("PRE TRANSLATE");
    console.log(processedHtml);
  }

  const options: Partial<NodeHtmlMarkdownOptions> = {
    bulletMarker: "-",
    ignore: ["title"],
    maxConsecutiveNewlines: 1,
  };

  // This lib needs Process to be present for some reason
  const backupProcess = globalThis.process;
  (globalThis as any).process = { env: {}, argv: [] };
  const rawMd = NodeHtmlMarkdown.translate(processedHtml, options);
  globalThis.process = backupProcess;

  if (rawHtmlContent.includes(htmlLogStr)) {
    console.log("POST TRANSLATE");
    console.log(rawMd);
  }

  const finalMd = sanitizeMd(rawMd);

  if (rawHtmlContent.includes(htmlLogStr)) {
    console.log(finalMd);
  }

  return finalMd;
}

function preprocessHtml(htmlContent: string) {
  // *** Alt lib does this automatically
  // const dom = new DOMParser().parseFromString(htmlContent, "text/html");
  // const titles = dom.getElementsByTagName("title");
  // while (titles.length > 0) {
  //   titles[0].parentNode?.removeChild(titles[0]);
  // }

  if (htmlContent.includes(htmlLogStr)) {
    console.log(htmlContent);
  }

  const firstReplaced = htmlContent
    .replaceAll(
      "\ufffc", // GraphAPI inserts after <a> tag instead of <br /> - bug?
      "<br />",
    )
    .replaceAll("---", "<hr />") // Don't escape --- with a backslash
    .replaceAll("<br>", brCode) // "new line" addition
    .replaceAll("<br />", brCode)
    .replaceAll(/<p[^>]*?>(&#160;|\s)+<\/p>/g, brCode) // para with just a nbsp - found in "Korean novels and fiction"
    .replaceAll("~", tildeCode)
    .replaceAll(".", periodCode);

  const withDataTags = preprocessHtmlAttachmentsAndDataTags(firstReplaced);

  return withDataTags;
}

// Should be idempotent - can run on all Joplin pages if I add something new.
export function sanitizeMd(rawMdContent: string) {
  if (rawMdContent.includes(htmlLogStr)) {
    console.log(rawMdContent);
  }

  const trimmed = rawMdContent
    .replaceAll(tildeCode, "~")
    .replaceAll(periodCode, ".")
    .replaceAll("\\[ \\] ", "[ ] ") // remove escapes on checkboxes
    .replaceAll("\\[x\\] ", "[x] ") // remove escapes on checkboxes
    .split("\n")
    .map(l => l.trimEnd().replace(/^\\> /, "> "))
    .join("\n")
    .replaceAll("\n!\n", "\n\n")
    .replaceAll(/\n\n\n+/g, "\n\n\n");
  if (rawMdContent.includes(htmlLogStr)) {
    console.log(trimmed);
  }

  // BR within P should be converted to newline, but BR on its own line
  // is just deleted to make it a newline
  const lines = trimmed.split("\n").map(l => (l.trim() === brCode ? "" : l));

  const withBr = lines.join("\n").replaceAll(brCode, "\n").trim();

  // after replacing brCode chars some lines may have space
  const trimmedMd = withBr
    .split("\n")
    .map(l => l.trimEnd())
    .join("\n")
    .replaceAll(/\n\n\n+/g, "\n\n\n");

  if (trimmedMd.includes(htmlLogStr)) {
    console.log(trimmedMd);
  }

  // can run in any order; all idempotent
  const finalSanitizers = [
    replaceFauxHeadings,
    replaceMultiply,
    removeStartOfLineSingleSpaces,
    addBrBeforeDates,
    elideDatesWithNoContent,
    appendFinalLineBreak,
    sanitizeDashes,
    sanitizeEscapedAsterisks,
  ];

  return finalSanitizers.reduce((prev, curr) => curr(prev), trimmedMd);
}

export function preprocessHtmlAttachmentsAndDataTags(rawHtml: string) {
  // last item will be furthest left, if there are multiple
  const dataTagSuffixMap: [string, string][] = [
    ["question", "?? "],
    ["remember-for-later", "!!! "],
    ["important", "!! "],
    ["to-do(?!:completed)", "[ ] "],
    ["to-do:completed", "[x] "],
  ];

  const withDataTagPrefixes = dataTagSuffixMap.reduce((prev, curr) => {
    return prev.replaceAll(
      new RegExp(`<(p|span)[^>]+?data-tag[^>]+?${curr[0]}[^>]+?>`, "g"),
      subStr => `${subStr}${curr[1]}`,
    );
  }, rawHtml);

  const withBoldSpans = addBoldTags(withDataTagPrefixes);

  return replaceOneNoteAttachmentTagsInHtml(withBoldSpans);
}

function replaceMultiply(mdStr: string) {
  const multiplyRegexSingle = /(\w)\\\*(\w)/;
  const multiplyRegexGlobal = makeRegexGlobal(multiplyRegexSingle);

  const charPairs: [string, string][] = [];

  while (true) {
    const result = multiplyRegexGlobal.exec(mdStr);
    if (!hv(result)) {
      break;
    }
    Assert.hasValue(result[1]);
    Assert.hasValue(result[2]);
    charPairs.push([result[1], result[2]]);
  }

  const finalStr = charPairs.reduce((acc, [lChar, rChar]) => {
    return acc.replace(multiplyRegexSingle, () => `${lChar} * ${rChar}`);
  }, mdStr);

  return finalStr;
}

function replaceFauxHeadings(mdStr: string) {
  const headingRegexSingle = /([^\n])\n-+\n/;
  const headingRegexGlobal = makeRegexGlobal(headingRegexSingle);

  const startingChars: string[] = [];

  while (true) {
    const result = headingRegexGlobal.exec(mdStr);
    if (!hv(result)) {
      break;
    }
    Assert.hasValue(result[1]);
    startingChars.push(result[1]);
  }

  const finalStr = startingChars.reduce((acc, char) => {
    return acc.replace(headingRegexSingle, () => `${char}\n\n---\n`);
  }, mdStr);

  return finalStr;
}

function removeStartOfLineSingleSpaces(mdStr: string) {
  const singleSpaceRegexSingle = /^ \S/;

  const lines = mdStr.split("\n");

  const newLines = lines.map(l =>
    singleSpaceRegexSingle.test(l) ? l.slice(1) : l,
  );

  return newLines.join("\n");
}

const baseDateRegex =
  /((\d\d?\.\w\w\w\.\d\d(\d\d)?)|(\d\d?\/\d\d?(\/\d\d(\d\d)?)?)|(\d\d?-\w\w\w-\d\d(\d\d)?)|(\(\d\d? \w\w\w '\d\d\))|(\w\w\w 20\d\d:?))/;
const baseDateRegexSubstr = baseDateRegex.toString().slice(1, -1); // without slashes

function addBrBeforeDates(mdStr: string) {
  // Examples:
  // 11.Aug.21
  // (\d\d?\.\w\w\w\.\d\d(\d\d)?)

  // 6/16/2016 or 6/13
  // (\d\d?\/\d\d?(\/\d\d\d\d)?)

  // 4-Feb-21
  // (3 Feb '21)

  const singleRegex = new RegExp(`([^\n])\n${baseDateRegexSubstr}`);

  let workingStr = mdStr;

  while (true) {
    const match = singleRegex.exec(workingStr);
    if (!hv(match)) {
      break;
    }

    workingStr = workingStr.replace(singleRegex, `$1\n\n$2`);
  }

  return workingStr;
}

function elideDatesWithNoContent(mdStr: string) {
  const singleRegex = new RegExp(
    `${baseDateRegexSubstr}\n+(${baseDateRegexSubstr})`,
  );

  let workingStr = mdStr;

  const fullDateGroupIndex = 11; // regex has many groups

  while (true) {
    const match = singleRegex.exec(workingStr);
    if (!hv(match)) {
      break;
    }

    const goodDateStr = match[fullDateGroupIndex];
    Assert.hard(hv(goodDateStr));

    workingStr = workingStr.replace(singleRegex, () => goodDateStr);
  }

  return workingStr;
}

function appendFinalLineBreak(mdStr: string) {
  return mdStr.slice(-1) === "\n" ? mdStr : `${mdStr}\n`;
}

function sanitizeDashes(mdStr: string) {
  return mdStr.replaceAll(/\s\\-\s/g, " - ");
}

function sanitizeEscapedAsterisks(mdStr: string) {
  return mdStr.replaceAll(/(\\\*)+\s/g, subStr =>
    subStr.replaceAll("\\*", "*"),
  );
}

export function addBoldTags(htmlContent: string) {
  // Examples:
  // <span style="font-weight:bold">
  const singleRegex =
    /(<span[^>]+?font-weight:bold[^>]+?>)(?!<strong>)(.+?)<\/span>/;
  // const singleRegex = /<span/g;
  let workingString = htmlContent;
  while (true) {
    const match = singleRegex.exec(workingString);
    if (!hv(match)) {
      break;
    }

    workingString = workingString.replace(
      singleRegex,
      `$1<strong>$2</strong></span>`,
      //`${spanOpenTag}<strong>${spanContent}</strong></span>`,
    );
  }

  return workingString;
}

export function replaceOneNoteAttachmentTagsInHtml(htmlStr: string) {
  // function for files OneNote -> Joplin

  const attachmentRegexSingle = /<object[^>]*?data-attachment="(.+?)".+?>/;
  const attachmentRegexGlobal = makeRegexGlobal(attachmentRegexSingle);

  const filenames: string[] = [];

  while (true) {
    const result = attachmentRegexGlobal.exec(htmlStr);
    if (!hv(result)) {
      break;
    }
    Assert.hasValue(result[1]);
    filenames.push(result[1]);
  }

  const finalStr = filenames.reduce((acc, filename) => {
    return acc.replace(
      attachmentRegexSingle,
      // Must be function so that dollar signs work correctly
      () => `&lt;file: "${filename}"&gt;`,
    );
  }, htmlStr);

  return finalStr;
}
