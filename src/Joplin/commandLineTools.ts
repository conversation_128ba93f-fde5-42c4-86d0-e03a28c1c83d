import { sortByDesc } from "../Comparable";
import { createJoplinUrlFromNoteId, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./Jo<PERSON>linShared";
import { exec } from "child_process"; // not available in browser

export function navigateToTopNote(notes: <PERSON><PERSON><PERSON><PERSON><PERSON>[]) {
  if (notes.length > 0) {
    // Nav to highest order (at top)
    const lastNote = notes.toSorted(sortByDesc(n => n.order))[0];
    navigateToNote(lastNote.id);
  }
}

export function navigateToNote(noteId: string) {
  // Construct the Jo<PERSON>lin note URL
  const joplinUrl = createJoplinUrlFromNoteId(noteId);

  // Determine the platform
  const platform =
    typeof process !== "undefined" ? process.platform : "browser";

  // Open the URL based on the platform
  switch (platform) {
    case "win32":
      // Windows
      exec(`start ${joplinUrl}`);
      break;
    case "darwin":
      // macOS
      exec(`open "${joplinUrl}"`);
      break;
    case "linux":
      // Linux
      exec(`xdg-open "${joplinUrl}"`);
      break;
    default:
      // Browser or unsupported platform
      window.open(joplinUrl, "_blank");
  }
}
