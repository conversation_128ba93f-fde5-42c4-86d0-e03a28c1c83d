import axios from "axios";
import { Assert, URLParamsInput, hasValueFn, hv, no } from "../Tools";
import { inSequence, sortBy } from "../Comparable";

export async function fetchLongJoplinList<T>(
  basePath: string,
  paramsInput: URLParamsInput = {},
): Promise<T[]> {
  const list: T[] = [];
  let hasMore = true;
  let page = 1;

  const limitParam =
    hasValueFn(new URLSearchParams(paramsInput).get("limit"), v =>
      parseInt(v),
    ) ?? null;

  if (hv(limitParam)) {
    // For now should be mult of 100
    Assert.hard(
      parseInt(new URLSearchParams(paramsInput).get("limit") ?? no()) % 100 ===
        0,
    );
  }

  while (hasMore) {
    const joinedParams = new URLSearchParams(paramsInput);
    joinedParams.delete("limit");
    joinedParams.append("limit", "100");
    joinedParams.append("page", page.toString());

    const foldersGetUrl = await createJoplinUrl(basePath, joinedParams);
    const result = await axios.get(foldersGetUrl);
    list.push(...result.data.items);
    hasMore =
      (result.data.has_more as boolean) &&
      (!hv(limitParam) || list.length < limitParam);
    ++page;
  }

  return list;
}

export interface JoplinFolder {
  title: string;
  id: string;
  parent_id: string;
}

export const joplinNoteFieldsValue =
  "id,title,body,parent_id,order,user_created_time,deleted_time";
export const jNoteFieldsObj = { fields: joplinNoteFieldsValue };
export const joplinFolderFieldsValue = "id,title,parent_id";
export const jFolderFieldsObj = { fields: joplinFolderFieldsValue };

export interface JoplinNote {
  id: string;
  body: string;
  title: string;
  parent_id: string;
  order: number;
  user_created_time: number;
  deleted_time: number; // 0 = not deleted
}

// import.meta is working fine both with Vite and direct Bun running
const joplinToken =
  import.meta.env.VITE_REACT_APP_JOPLIN_TOKEN ?? no("no token found");

// bun only understands an IP for localhost
const baseUrl = "http://127.0.0.1";

// Function to detect the running port of the Joplin Web Clipper
async function findJoplinPort(): Promise<number> {
  for (let portToTest = 41184; portToTest <= 41194; portToTest++) {
    try {
      const url = `${baseUrl}:${portToTest}/ping`;
      const response = await fetch(url, { method: "get" });
      if (response.status === 200) {
        return portToTest;
      }
    } catch (errorUnknown) {
      // // Go on to next port
    }
  }
  throw new Error("No Joplin running port found");
}

export async function createJoplinUrl(
  path: string,
  paramsInput: URLParamsInput = {},
): Promise<string> {
  const joplinPort = await findJoplinPort();
  const params = new URLSearchParams(paramsInput);
  Assert.hard(!path.startsWith("/"), "Path cannot start with '\\'");
  params.append("token", joplinToken);

  return `${baseUrl}:${joplinPort}/${path}?${params.toString()}`;
}

export async function fetchJoplinNote(noteId: string): Promise<JoplinNote> {
  const noteUrl = await createJoplinUrl(`notes/${noteId}`, {
    fields: joplinNoteFieldsValue,
  });
  return (await axios.get(noteUrl)).data;
}

export async function createJoplinNoteAtBottom(
  input: Omit<Partial<JoplinNote>, "id" | "order"> &
    Required<Pick<JoplinNote, "body" | "parent_id">> & {
      user_created_time?: number;
    },
) {
  const existingNotes = await fetchNotesInFolder(input.parent_id);
  const { getNewOrderFromIndex } = await mergeNewBottomNotesOrder(
    existingNotes,
    1 /*newNotesLength*/,
  );
  return createJoplinNote({ ...input, order: getNewOrderFromIndex(0) });
}

export async function createJoplinNoteAtTop(
  input: Omit<Partial<JoplinNote>, "id" | "order"> &
    Required<Pick<JoplinNote, "body" | "parent_id">> & {
      user_created_time?: number;
    },
) {
  const existingNotes = await fetchNotesInFolder(input.parent_id);
  const maxOrder =
    existingNotes.length === 0
      ? 0
      : Math.max(...existingNotes.map(n => n.order));
  return createJoplinNote({ ...input, order: maxOrder + 1 });
}

export async function createJoplinNote(
  input: Omit<Partial<JoplinNote>, "id"> &
    Required<Pick<JoplinNote, "body" | "parent_id">> & {
      user_created_time?: number;
    },
): Promise<JoplinNote> {
  const url = await createJoplinUrl(`notes`, jNoteFieldsObj);
  const { order, ...fields } = input;

  const result = await axios.post(url, {
    ...fields,
    title: fields.title ?? getTitleFromBody(fields.body),
  });

  const newNote = result.data;

  if (!hv(order)) {
    return newNote;
  }

  // Apparently it's possible to patch order, but not to create it...
  return patchJoplinNote(newNote.id, { order });
}

export function getTitleFromBody(body: string) {
  return body.trim().split("\n")[0].slice(0, 100);
}

export async function fetchNotesInFolder<T = JoplinNote>(
  folderId: string,
  paramsInput: URLParamsInput = {},
): Promise<T[]> {
  const params = new URLSearchParams(paramsInput);
  if (!params.has("fields")) {
    params.append("fields", joplinNoteFieldsValue);
  }
  return await fetchLongJoplinList<T>(`folders/${folderId}/notes`, params);
}

export async function patchJoplinNote(
  noteId: string,
  data: Partial<JoplinNote>,
): Promise<JoplinNote> {
  const url = await createJoplinUrl(`notes/${noteId}`, {
    fields: joplinNoteFieldsValue,
  });
  const putResult = await axios.put(url, data);
  return putResult.data;
}

export async function fetchAllJoplinFolders<
  T = JoplinFolder & { children: any },
>(paramsInput: URLParamsInput = {}): Promise<T[]> {
  return await fetchLongJoplinList<T>("folders", paramsInput);
}

export function toSortedNotesInFolder(
  unsortedNotes: JoplinNote[],
): JoplinNote[] {
  return unsortedNotes.toSorted(
    sortBy(n => inSequence(n.order, n.user_created_time)),
  );
}

export function getNoteIdsFromString(arg: string) {
  const matchResult = arg.matchAll(/\b[a-f0-9]{32}\b/g);
  return Array.from(matchResult).flat();
}

export function getNoteIdsFromCommandLineArg() {
  const markdownLinkText = process.argv[2];

  return getNoteIdsFromString(markdownLinkText);
}

export async function fetchOrderedSourceNotesFromMarkdownLinkArg() {
  const noteIds = getNoteIdsFromCommandLineArg();

  // these are copied in unpredictable order
  const unsortedNotes = await Promise.all(
    noteIds.map(id => fetchJoplinNote(id)),
  );

  return toSortedNotesInFolder(unsortedNotes);
}

export async function moveSelectedNotesToFolder(folderId: string) {
  const sourceNotes = await fetchOrderedSourceNotesFromMarkdownLinkArg();
  return await moveNotesToFolder(sourceNotes, folderId);
}

export async function moveNotesToFolder(
  sourceNotes: { id: string }[],
  folderId: string,
) {
  // This method patches SourceNotes twice which I think is generally preferable
  // because the initial move then happens faster
  await Promise.all(
    sourceNotes.map(source =>
      patchJoplinNote(source.id, {
        parent_id: folderId,
      }),
    ),
  );

  const existingNotes = (await fetchJoplinNotesInFolder(folderId)).filter(
    n => !sourceNotes.map(({ id }) => id).includes(n.id),
  );

  const { getNewOrderFromIndex } = await mergeNewBottomNotesOrder(
    existingNotes,
    sourceNotes.length,
  );

  return await Promise.all(
    sourceNotes.map(async (source, i) => {
      const order = getNewOrderFromIndex(i);
      return await patchJoplinNote(source.id, { order });
    }),
  );
}

export async function copyNotesToFolder(
  sourceNotes: JoplinNote[],
  folderId: string,
) {
  // minOrder might be ZERO which is kind of crazy...
  const orderedDestFolderNotes = await fetchJoplinNotesInFolder(folderId);

  const { getNewOrderFromIndex } = await mergeNewBottomNotesOrder(
    orderedDestFolderNotes,
    sourceNotes.length,
  );

  return await Promise.all(
    sourceNotes.map(async (n, i) => {
      const order = getNewOrderFromIndex(i);
      return await createJoplinNote({
        body: n.body,
        user_created_time: n.user_created_time,
        title: n.title,
        parent_id: folderId,
        order,
      });
    }),
  );
}

export async function copySelectedNotesToFolder(folderId: string) {
  const sourceNotes = await fetchOrderedSourceNotesFromMarkdownLinkArg();

  return copyNotesToFolder(sourceNotes, folderId);
}

export async function mergeNewBottomNotesOrder(
  existingNotes: JoplinNote[],
  newNotesLength: number,
) {
  const areAllNotesOrdered = existingNotes.every(n => n.order > 0);

  let existingNotesOrderMin: number;
  if (existingNotes.length === 0) {
    existingNotesOrderMin = 1;
  } else if (!areAllNotesOrdered) {
    existingNotesOrderMin = newNotesLength + 1;
    // Same as Move
    await Promise.all(
      toSortedNotesInFolder(existingNotes).map((n, i) =>
        patchJoplinNote(n.id, { order: existingNotesOrderMin + i }),
      ),
    );
  } else {
    existingNotesOrderMin = Math.min(...existingNotes.map(n => n.order));
  }

  return {
    getNewOrderFromIndex: (noteIndex: number) =>
      ((noteIndex + 1) / (newNotesLength + 1)) * existingNotesOrderMin,
  };
}

export async function fetchJoplinNotesInFolder(
  folderId: string,
): Promise<JoplinNote[]> {
  return await fetchLongJoplinList(`folders/${folderId}/notes`, jNoteFieldsObj);
}

export function createJoplinUrlFromNoteId(noteId: string) {
  return `joplin://x-callback-url/openNote?id=${encodeURIComponent(noteId)}`;
}
