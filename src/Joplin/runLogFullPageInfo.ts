import { format } from "date-fns";
import { createJoplinUrl, getNoteIdsFromCommandLineArg } from "./JoplinShared";
import axios from "axios";

async function logFullPageInfo() {
  const noteIds = getNoteIdsFromCommandLineArg();

  const notes: {
    id: string;
    title: string;
    parent_id: string;
    order: number;
    created_time: number;
    user_created_time: number;
    updated_time: number;
  }[] = await Promise.all(
    noteIds.map(async id => {
      const noteUrl = await createJoplinUrl(`notes/${id}`, {
        fields: `id,title,parent_id,order,created_time,user_created_time,updated_time`,
      });
      return (await axios.get(noteUrl)).data;
    }),
  );

  if (notes.length === 1) {
    console.log(`${format(notes[0].user_created_time, "yyyy-M-d")}\n`);
  }

  console.log(
    notes.map(n => ({
      ...n,
      created_time: new Date(n.created_time),
      user_created_time: new Date(n.user_created_time),
      updated_time: new Date(n.updated_time),
    })),
  );
}

void logFullPageInfo();
