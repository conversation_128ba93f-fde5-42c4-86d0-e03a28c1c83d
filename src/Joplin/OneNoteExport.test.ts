import { getNoteIdsFromString } from "./JoplinShared";
import {
  addBoldTags,
  getMdFromHtml,
  preprocessHtmlAttachmentsAndDataTags,
  sanitizeMd,
} from "./getMdFromHtml";

test("sanitizeMd", () => {
  const testStrings = [
    "\n\n\\[ \\] something\n\n   \n\n\\> else 2\\*3=6\n\n",
    "\n\nsomething\n\n새새새\nelse a\\*b=c",
    "\n\nsomething새새새\n\n\n\nelse",
    `8.Apr.23
\\> But I wish <PERSON><PERSON> had never retired from work and come here to grow vegetable marrows.

Ok this one seems rly worthy`,
    `some text
---
I don't use headers like that`,
    `some text

---
also not a header`,
    `---
Source: How Big Oil Misled The Public Into Believing Plastic Would Be Recycled`,
    ` single space

 ---
also single space`,
    `test
1.Jan.2023
too bunched
29.Dec.2020
also bunched
6/16/2016
old style
6/13
very old style
4-Feb-21
briefly used style
(3 Feb '21)
another briefly used style
29-Jan-21
done
yay`,
    // consecutive dates - elide
    `some content
14.Feb.2017 
1.Jan.2023




4-Feb-21
Feb 2020:
Jan 2021
(3 Feb '21)
6/16/2016
8/9/16 
primary goal...`,
    "bad backslash \\- something\nshould delete \\- this",
    `test
!
exclamation (bold)`,
    `test

!

exclamation (bold)`,
  ];

  const expectedStrings = [
    "[ ] something\n\n\n> else 2 * 3=6\n",
    "something\n\n\nelse a * b=c\n",
    "something\n\n\nelse\n",
    `8.Apr.23
> But I wish Hercule Poirot had never retired from work and come here to grow vegetable marrows.

Ok this one seems rly worthy\n`,
    `some text

---
I don't use headers like that\n`,
    `some text

---
also not a header\n`,
    `---
Source: How Big Oil Misled The Public Into Believing Plastic Would Be Recycled\n`,
    `single space

---
also single space\n`,
    `test

1.Jan.2023
too bunched

29.Dec.2020
also bunched

6/16/2016
old style

6/13
very old style

4-Feb-21
briefly used style

(3 Feb '21)
another briefly used style

29-Jan-21
done
yay\n`,
    `some content

8/9/16
primary goal...\n`,
    "bad backslash - something\nshould delete - this\n",
    `test

exclamation (bold)
`,
    `test


exclamation (bold)
`,
  ];

  testStrings.forEach((s, i) => {
    const result = sanitizeMd(s);
    expect(result).toEqual(expectedStrings[i]);
    expect(sanitizeMd(result)).toEqual(expectedStrings[i]);
  });
});

test("strip title", () => {
  expect(getMdFromHtml("<title>foo</title><p>no extra</p>")).toEqual(
    "no extra\n",
  );
});

test("newlines (only BR causes double)", () => {
  const input = "<p>foo</p><p>bar<br /></p><p>baz</p><p>bat</p>";
  const expected = `foo
bar

baz
bat\n`;
  expect(getMdFromHtml(input)).toEqual(expected);
});

test("no escaping a period", () => {
  expect(getMdFromHtml("<p>Man this is a 7/7. </p>")).toEqual(
    "Man this is a 7/7.\n",
  );
});

test("preprocess", () => {
  const testHtml =
    //'ab ac ad'
    `start

<object /> test <object data-attachment="first file.csv" type="application/octet-stream" data="https://graph.microsoft.com/v1.0/users('<EMAIL>')/onenote/resources/0-8695701d074d49498cb76988b1ef615c!1-4DC6B457EA38D0AC!307032/$value" /> (something)

<p data-tag="important">this is starred</p>

<p data-tag="to-do">this is a checkbox</p>

<p data-tag="remember-for-later" style="$$">this is highlighted in yellow</p>

<object data-attachment="second $$ file.csv" type="application/octet-stream" data="https://graph.microsoft.com/v1.0/users('<EMAIL>')/onenote/resources/0-40cdb0e336398c4b84286e071f9c8e05!1-4DC6B457EA38D0AC!307032/$value" />

<br />`;

  const expected = `start

<object /> test &lt;file: "first file.csv"&gt; (something)

<p data-tag="important">!! this is starred</p>

<p data-tag="to-do">[ ] this is a checkbox</p>

<p data-tag="remember-for-later" style="$$">!!! this is highlighted in yellow</p>

&lt;file: "second $$ file.csv"&gt;

<br />`;

  expect(preprocessHtmlAttachmentsAndDataTags(testHtml)).toEqual(expected);
});

test("bold in getMd", () => {
  const input = `<p style="margin-top:0pt;margin-bottom:0pt"><span style="font-weight:bold">$241 pushed</span></p>`;
  const expected = `<p style="margin-top:0pt;margin-bottom:0pt"><span style="font-weight:bold"><strong>$241 pushed</strong></span></p>`;
  expect(addBoldTags(input)).toEqual(expected);
});

test("Full conversions HTML -> MD", () => {
  const input = `<p>*** important stuff</p>`;
  const expected = `*** important stuff
`;

  expect(getMdFromHtml(input)).toEqual(expected);
});

test("Scrunched Korean page", () => {
  const input = `<p>About jeju island divers</p>

<p>&quot;will give oyu nightmares&quot;</p>

<p>&#160;</p>

<p>  &#160;           &#160;&#160;&#160; &#160;  </p>

<p>Short stories IN KOREAN</p>`;

  const expected = `About jeju island divers
"will give oyu nightmares"


Short stories IN KOREAN
`;

  expect(getMdFromHtml(input)).toEqual(expected);
});

test("Missing star with <li>", () => {
  const input = `<li><span data-tag="important">just disconnect</span></li>`;
  const expected = "- !! just disconnect\n";

  expect(getMdFromHtml(input)).toEqual(expected);
});

test("multiple data-tag indicators", () => {
  const input = `<p data-tag="important,question" style="">Maestro</p>`;
  const expected = `!! ?? Maestro\n`;

  expect(getMdFromHtml(input)).toEqual(expected);
});

test("word 'question' in middle", () => {
  const input = `<p data-tag="important">has the word 'question'</p>

<br />

<p data-tag="important,question" style="">Maestro</p>`;
  const expected = `!! has the word 'question'

!! ?? Maestro
`;

  expect(getMdFromHtml(input)).toEqual(expected);
});

test("completed checkbox", () => {
  const input = `<li><span data-tag="to-do:completed">KM</span></li>

<li><span data-tag="to-do:completed">Mute my Mic</span></li>`;

  const expected = `- [x] KM
- [x] Mute my Mic
`;

  expect(getMdFromHtml(input)).toEqual(expected);
});

test("Get page IDs", () => {
  const linkText =
    "[some](:/c8e0f25b5a24463589289fab37457614) [page 2](:/d3e685a305214389b3657e3193635fee) [](:/64cda1588a744fb096e5ffacb1aaf148) [](:/8fda46e5f8fb4464a37dce48aa76e5ba) [page 3](:/afb4020ac5d64533b6156c9c484d6388)";

  const expected = [
    "c8e0f25b5a24463589289fab37457614",
    "d3e685a305214389b3657e3193635fee",
    "64cda1588a744fb096e5ffacb1aaf148",
    "8fda46e5f8fb4464a37dce48aa76e5ba",
    "afb4020ac5d64533b6156c9c484d6388",
  ];
  expect(getNoteIdsFromString(linkText)).toEqual(expected);

  const singleLinkText = "[test](:/afb4020ac5d64533b6156c9c484d6388)";
  const singleLinkExpected = ["afb4020ac5d64533b6156c9c484d6388"];
  expect(getNoteIdsFromString(singleLinkText)).toEqual(singleLinkExpected);
});
