import { BatchRequestStep, Client } from "@microsoft/microsoft-graph-client";
import { OnenotePage } from "microsoft-graph";
import { submitBatchRequests } from "../GraphApiHelpers";
import { Assert, decodeBase64ToUtf8, hv, no } from "../Tools";
import { parseISO } from "date-fns";
import { sortBy } from "../Comparable";

export interface InProgressSectionData {
  sectionId: string | null;
  cachedMetadata: any[];
  cachedContent: any[];
  nextMetadataLink: string | null;
}

export const onenoteToSkipPageIds = [
  "0-fca857ab53c656f894047afbee6e0ffe!1-A0AC2C4AD3A18661!32443", // unknownError in Aimee folder
  "0-804d932f16a655fb91bd0a1a1babe7b5!1-A0AC2C4AD3A18661!32443", // Same
  "0-321a15e147e409921f962d06fd07bd1a!1-4DC6B457EA38D0AC!175972", // Same
  "0-0cd42610209b440e97b4f740f5809240!1-A0AC2C4AD3A18661!23814", // unknown error for misc promotion page
  "0-f15c6ac383004c5fa5579696723e904c!1-A0AC2C4AD3A18661!23814", // same
  "0-ff7f14f8ff5041f59195f7f8ad03ab9c!33-A0AC2C4AD3A18661!33955", // in Black, unknown err
  "0-2b20c878553540cdaa246592124d2c5f!114-A0AC2C4AD3A18661!33955", // same
  "0-6e2231d2919e410398a9770cae0ca2a7!1-A0AC2C4AD3A18661!23595", //not found in journals
  " 0-7ea2ae1aa0c39248839071f30bcf354c!1-A0AC2C4AD3A18661!34768 ", // endowment eff
  "0-d91cc311f067bc468132669a330994e4!1-4DC6B457EA38D0AC!311031", // from What I Want folder
];

export async function fetchOneNotePagesInOrderForSection(
  sectionId: string,
  client: Client,
  cumulativeData?: InProgressSectionData,
  handleCumulativeData?: (data: InProgressSectionData) => Promise<unknown>,
) {
  let rawSortedMetadataResults: any[];

  if (
    hv(cumulativeData) &&
    cumulativeData.cachedMetadata.length > 0 &&
    !hv(cumulativeData.nextMetadataLink)
  ) {
    // we have metadata and NO next link, so it is complete.
    rawSortedMetadataResults = cumulativeData.cachedMetadata;
  } else {
    let response: { value: OnenotePage[] } & Record<string, unknown>;

    let resultArr: OnenotePage[];

    if (hv(cumulativeData?.nextMetadataLink)) {
      response = await client.api(cumulativeData.nextMetadataLink).get();
      resultArr = [...cumulativeData.cachedMetadata, ...response.value];
    } else {
      Assert.hard(
        cumulativeData?.cachedMetadata.length === 0,
        "Unexpected partial metadata with no link",
      );
      response = await client
        .api(`/me/onenote/sections/${sectionId}/pages?pagelevel=true`)
        .get();

      resultArr = [...response.value];
    }

    while (true) {
      const entriesToSearch = Object.entries(response);
      const nextLink: string = entriesToSearch.find(
        e => e[0] === "@odata.nextLink",
      )?.[1] as string;

      rawSortedMetadataResults = resultArr.toSorted(
        sortBy(r => r.order ?? no(`no ORDER for ${r.id}`)),
      );

      await handleCumulativeData?.({
        cachedContent: [],
        cachedMetadata: rawSortedMetadataResults,
        sectionId,
        nextMetadataLink: nextLink ?? null,
      });

      if (!hv(nextLink)) {
        break;
      }

      response = await client.api(nextLink).get();
      resultArr.push(...response.value);
    }
  }

  const sortedMetadataResults = (rawSortedMetadataResults ?? []).filter(
    result => !onenoteToSkipPageIds.includes(result.id),
  );

  const rawContentRequestSteps: BatchRequestStep[] = sortedMetadataResults.map(
    p => ({
      request: new Request(`/me/onenote/pages/${p.id}/content`, {
        method: "get",
      }),
      id: p.id ?? no("no ID for page"),
    }),
  );

  const initialCachedResponses = cumulativeData?.cachedContent ?? [];

  console.log(`Pre dedup count: ${rawContentRequestSteps.length}`);

  const dedupedContentRequestSteps = rawContentRequestSteps.reduce(
    (acc, curr) => {
      if (acc.pageIdSet.has(curr.id)) {
        console.log("Duplicate page found; removing");
        console.log(curr.request);

        return acc;
      }

      acc.pageIdSet.add(curr.id);
      acc.steps.push(curr);

      return acc;
    },
    { pageIdSet: new Set<string>(), steps: [] as BatchRequestStep[] },
  );

  console.log(`Post dedup count: ${dedupedContentRequestSteps.steps.length}`);

  const contentBatchResults: OnenotePageContentBatchResult[] =
    await submitBatchRequests(
      dedupedContentRequestSteps.steps,
      client,
      initialCachedResponses,
      async (cumulativeContent: any[]) => {
        await handleCumulativeData?.({
          cachedContent: cumulativeContent,
          cachedMetadata: rawSortedMetadataResults,
          nextMetadataLink: null,
          sectionId,
        });
      },
    );

  // Add in reverse order because Joplin puts most recent at top
  const pages = sortedMetadataResults.toReversed().map(p => {
    const pageContent =
      contentBatchResults.find(({ id }) => id === p.id) ??
      no(`Could not find ID: ${p.id}`);

    if (!hv(pageContent.body)) {
      console.error("Undefined body");
      console.error(pageContent);
      throw new Error("Bailing out");
    }

    const decodedBody = decodeBase64ToUtf8(pageContent.body);

    return {
      htmlContent: decodedBody,
      title: p.title ?? no("No title"),
      createdDateTime: parseISO(p.createdDateTime ?? no()),
      level: p.level ?? no("no level"),
      id: p.id ?? no("no ID"),
    };
  });

  return pages;
}

export interface OnenotePageContentBatchResult {
  id: string; // request ID
  status: string;
  body: string; // base 64 encoded HTML
}
