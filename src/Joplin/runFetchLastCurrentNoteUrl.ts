import { Assert } from "../Tools";
import { J_CURRENT_FOLDER_ID } from "./Constants";
import { fetchNotesInFolder, toSortedNotesInFolder } from "./JoplinShared";

async function fetchLastCurrentNoteUrl() {
  const notes = await fetchNotesInFolder(J_CURRENT_FOLDER_ID);
  Assert.hard(notes.length > 0);

  const noteId = toSortedNotesInFolder(notes).slice(-1)[0].id;

  return `joplin://x-callback-url/openNote?id=${noteId}`;
}

void fetchLastCurrentNoteUrl().then(url => console.log(url));
