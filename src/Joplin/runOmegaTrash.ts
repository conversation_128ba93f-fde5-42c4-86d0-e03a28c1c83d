import {
  fetchL<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  j<PERSON><PERSON><PERSON><PERSON><PERSON>ieldsValue,
  patchJ<PERSON><PERSON><PERSON>ote,
} from "./Jo<PERSON>linShared";
import { resolveInOrder } from "../Tools";
import { J_OMEGA_TRASH_FOLDER_ID } from "./Constants";

// Moves TRASHED notes to Omega...
// This avoids sort-order issues per https://github.com/laurent22/joplin/issues/11226
// Note this takes only 1/2 second so seems like great tradeoff
async function runOmegaTrash() {
  const allNotes = await fetchLongJoplinList<
    JoplinNote & { deleted_time: number }
  >("notes", {
    include_deleted: "1",
    fields: `${joplinNoteFieldsValue},deleted_time`,
    order_by: "updated_time",
    order_dir: "DESC",
    limit: "500", // should be much more than # of Deleted I deal with at once... even if not it will catch up.
  });

  const movableNotes = allNotes.filter(
    n => n.deleted_time > 0 && n.parent_id !== J_OMEGA_TRASH_FOLDER_ID,
  );

  await resolveInOrder(movableNotes, async n =>
    patchJoplinNote(n.id, { parent_id: J_OMEGA_TRASH_FOLDER_ID }),
  );
}

void runOmegaTrash();
