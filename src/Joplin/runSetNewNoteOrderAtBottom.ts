import { sortBy } from "../Comparable";
import {
  fetchNotesInFolder,
  fetchOrderedSourceNotesFromMarkdownLinkArg,
  mergeNewBottomNotesOrder,
  patchJoplinNote,
  toSortedNotesInFolder,
} from "./JoplinShared";

// Not working - puts in wrong order AND prob bad approach b/c it messes up
// the "creating new page" state...
async function runSetNewNoteAtBottomOrder() {
  const selectedNote = (await fetchOrderedSourceNotesFromMarkdownLinkArg())[0];
  const rawFolderNotes = await fetchNotesInFolder(selectedNote.parent_id);

  // 0-based
  const newNote = rawFolderNotes.toSorted(sortBy(n => -n.user_created_time))[0];

  const sortedPreexistingNotes = toSortedNotesInFolder(rawFolderNotes).filter(
    n => n.id !== newNote.id,
  );

  const { getNewOrderFromIndex } = await mergeNewBottomNotesOrder(
    sortedPreexistingNotes,
    1,
  );

  await patchJoplinNote(newNote.id, { order: getNewOrderFromIndex(0) });
}

void runSetNewNoteAtBottomOrder();
