import { TaggedInput } from "./Components/getPatchForTaggedInput";
import { getRawLowerAudioTags } from "./getAudioTags";
import {
  getCustomDateFromText,
  getSortedRawLowerTagsForText,
  getTaggedInputs,
  isDateOnlyLine,
} from "./getTags";
import { NoteInput } from "./NoteInput";
import { hasDiaryIndicator, isFoundTag } from "./TagNotePairHelpers";

test("Basic getTags", () => {
  // .t. removed 15.Jun.2021 - I use it to mean "thought [from me]"
  const tags = getSortedRawLowerTagsForText(
    "!!myTag... .z.tagwitháccent .t.anothertagnotpulled! .M.UPPERm .m.NEWTAGISM Here's text !!MySeCoNdTaG... ....z.mythirdtag? .Z.upperZ !!myTag DUPLICATE @@attag",
  );

  // No double bang anymore
  expect(tags).toEqual([
    "attag",
    "mysecondtag",
    "mytag",
    "mythirdtag",
    "newtagism",
    "tagwitháccent",
    "upperm",
    "upperz",
  ]);
});

test("Semicolon getTags", () => {
  const tags = getSortedRawLowerTagsForText(";;myTag... !!dupetag !!dupetag");

  // No double bang anymore
  expect(tags).toEqual(["dupetag", "mytag"]);
});

test("get tag from highlight", () => {
  const tags = getSortedRawLowerTagsForText("@@bold @@autoparse has a tag!");

  expect(tags).toEqual(["autoparse", "bold"]);
});

test("getSortedTagsForText: Reject partial tagging", () => {
  expect(() => getSortedRawLowerTagsForText("11 p.m.")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText("10 a.m.")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText(".z. autoparse")).toThrow();
  expect(() => getSortedRawLowerTagsForText("something.z.")).toThrow();
  expect(() => getSortedRawLowerTagsForText(" .z. autoparse")).toThrow();
  expect(() => getSortedRawLowerTagsForText(" z.autoparse")).toThrow();
  expect(() => getSortedRawLowerTagsForText(".m. autoparse")).toThrow();
  expect(() => getSortedRawLowerTagsForText("something.m.")).toThrow();
  expect(() => getSortedRawLowerTagsForText(" .m. autoparse")).toThrow();
  expect(() => getSortedRawLowerTagsForText(" m.autoparse")).toThrow();
  expect(() => getSortedRawLowerTagsForText("David M. Buss")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText("David M.. Buss")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText("@@ autoparse")).toThrow();
  expect(() => getSortedRawLowerTagsForText(" @@ autoparse")).toThrow();
  expect(() => getSortedRawLowerTagsForText(";; autoparse")).toThrow();
  expect(() => getSortedRawLowerTagsForText(" ;; autoparse")).toThrow();
  expect(() => getSortedRawLowerTagsForText("!! autoparse")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText("something;; autoparse")).toThrow();
  expect(() => getSortedRawLowerTagsForText(" !! autoparse")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText("something@@ autoparse")).toThrow();
  expect(() => getSortedRawLowerTagsForText(" !! autoparse")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText(".zVery partial prefix")).toThrow();
  expect(() => getSortedRawLowerTagsForText("z.Very partial prefix")).toThrow();
  expect(() => getSortedRawLowerTagsForText(".mVery partial prefix")).toThrow();
  expect(() => getSortedRawLowerTagsForText("m.Very partial prefix")).toThrow();
  expect(() => getSortedRawLowerTagsForText(".ZVery partial prefix")).toThrow();
  expect(() => getSortedRawLowerTagsForText("Z.Very partial prefix")).toThrow();
  expect(() => getSortedRawLowerTagsForText(".MVery partial prefix")).toThrow();
  expect(() => getSortedRawLowerTagsForText("M.Very partial prefix")).toThrow();
  expect(() =>
    getSortedRawLowerTagsForText("!Very partial prefix"),
  ).not.toThrow();
  expect(() => getSortedRawLowerTagsForText("..Very partial prefix")).toThrow(); // Still important I think, just found example early Aug 2022.  Change my "" quote representation maybe just single .
  expect(() => getSortedRawLowerTagsForText("rules..n.")).not.toThrow(); // actual example day after I implemented prefix tests
  expect(() => getSortedRawLowerTagsForText("airbnb.me")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText("1.Mar.22")).not.toThrow(); // date is ok
  expect(() => getSortedRawLowerTagsForText("@twitterhandle")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText("...this is fine")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText("something.z.b")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText("something.m.b")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText(".z.zeetag")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText(".m.metag")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText("something@@b")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText("something!!b")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText("@@machinelearning")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText("!!zoos")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText("wow!!!")).not.toThrow();
  expect(() =>
    getSortedRawLowerTagsForText(".z.my thoughts on this"),
  ).not.toThrow();
  expect(() => getSortedRawLowerTagsForText(".m.zis is my note")).not.toThrow();
  expect(() =>
    getSortedRawLowerTagsForText("well ...men are visual"),
  ).not.toThrow();

  expect(() => getSortedRawLowerTagsForText(".záutoparse")).toThrow();
  expect(() => getSortedRawLowerTagsForText("z.áutoparse")).toThrow();
  expect(() => getSortedRawLowerTagsForText("..áutoparse")).toThrow();
  expect(() => getSortedRawLowerTagsForText("@@áutoparse")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText(";;áutoparse")).not.toThrow();
  expect(() => getSortedRawLowerTagsForText(";áutoparse")).toThrow();
  expect(() => getSortedRawLowerTagsForText(".z.áutoparse")).not.toThrow();

  expect(() => getSortedRawLowerTagsForText(";autoparse")).toThrow();
  expect(() =>
    getSortedRawLowerTagsForText("Some words first then ;autoparse"),
  ).toThrow();
});

test("sorted tags w para specific/one-off", () => {
  const date = "2022-08-01";
  const baseInput: NoteInput = {
    highlightText: "just one tag @@autoparse",
    noteText: `some para with @@p @@o tag
another @@o@@b
third @@oneoff@@p @@ltr
fourth with a normal tag @@u`,
    date,
  };

  const result = getTaggedInputs(baseInput);

  expect(result.taggedInputs).toEqual([
    { localTags: ["autoparse", "u"], input: baseInput, universalTags: [] },
    {
      localTags: ["p"],
      input: {
        ...baseInput,
        noteText: "some para with @@p @@o tag",
      },
      universalTags: [],
    },
    {
      localTags: ["b"],
      input: {
        ...baseInput,
        noteText: "another @@o@@b",
      },
      universalTags: [],
    },
    {
      localTags: ["ltr", "p"],
      input: {
        ...baseInput,
        noteText: "third @@oneoff@@p @@ltr", // no combining para tags for now
      },
      universalTags: [],
    },
  ]);
});

test("para tag throw conditions", () => {
  expect(() =>
    getTaggedInputs({
      highlightText: "invalid one-off in highlight @@o @@u",
    }),
  ).toThrow();
  expect(() =>
    getTaggedInputs({
      noteText: "missing descriptor tag for one-off @@o",
    }),
  ).toThrow();
});

test("only contains one-offs, so remove main patch", () => {
  const baseInput: NoteInput = {
    noteText: "one-off only @@o @@u\n\nanother one-off @@o @@p",
    date: "2022-08-02",
  };
  const expectedInputs: TaggedInput[] = [
    {
      input: { noteText: "one-off only @@o @@u", date: "2022-08-02" },
      localTags: ["u"],
      universalTags: [],
    },
    {
      input: { noteText: "another one-off @@o @@p", date: "2022-08-02" },
      localTags: ["p"],
      universalTags: [],
    },
  ];
  expect(getTaggedInputs(baseInput).taggedInputs).toEqual(expectedInputs);
});

test("ALL tag with one-off tag disallowed", () => {
  expect(() => getTaggedInputs({ noteText: "@@all high @@o @@p" })).toThrow();
});

test("strip universal tags from results", () => {
  const expected: TaggedInput = {
    input: { noteText: "@@all @@u\n@@p" },
    localTags: ["p"],
    universalTags: ["u"],
  };

  expect(
    getTaggedInputs(
      { noteText: "@@all @@u\n@@p" },
      { shouldAllowUniversal: true },
    ).taggedInputs,
  ).toEqual([expected]);
});

test("invalid tags", () => {
  expect(isFoundTag("NOTATAG")).toBeFalsy();
  expect(isFoundTag("a")).toBeTruthy();
  expect(isFoundTag("o")).toBeTruthy();
  expect(isFoundTag("p")).toBeTruthy();
});

test("diary indicator", () => {
  expect(hasDiaryIndicator("@@D @@p")).toEqual(true);
  expect(hasDiaryIndicator("@@business note")).toEqual(false);
});

test("taggedInput universal", () => {
  const result = getTaggedInputs(
    { noteText: "@@all @@u" },
    { shouldAllowUniversal: true, shouldFindAudioTags: false },
  );
  expect(result.taggedInputs?.[0]?.universalTags).toEqual(["u"]);
});

const audioTestCases: string[] = [
  // Simple case
  "Remember//LTR agree  ",
  // Word slashes from Alexa
  "learn SLASH SLASH Spanish",
  // Special chars inserted by Siri
  "helpful here//(CDMX)! apartment",
  // Multiple tags
  "multiple//business, management",
  // mid-note tag
  "I definitely like this//boxing drill/slash  with a straight a hook",
  // Multiple sets of tags
  "//teetotaling//I am sure something something//sleep",
  // Partial word separator
  "This regards Slash/autoparse// and the note continues",
  // close then open immediately
  "something //one set////another tag// and done  ",
  // word comma
  "something //b comma u comma business",
  // apostrophe from Siri
  "for me//Marisol's next",
  // word "come a" mishearing
  "something //b come a p come a w",
  "Test for whale book //we'll book",
  "URL test //bachlearn// https://www.instagram.com/reel/DBTaDVRCaGy/?igsh=c25pYnJ0Ync0M2E0",
];

const expectedAudioRawTags: string[][] = [
  ["ltragree"],
  ["spanish"],
  ["cdmxapartment"],
  ["business", "management"],
  ["boxingdrill"],
  ["teetotaling", "sleep"],
  ["autoparse"],
  ["oneset", "anothertag"],
  ["b", "u", "business"],
  ["marisolsnext"],
  ["b", "p", "w"],
  ["wellbook"],
  ["bachlearn"],
];

test("find separators", () => {
  // Too many slashes - possible bad audio note
  expect(() => getRawLowerAudioTags("///")).toThrow();
  expect(() => getRawLowerAudioTags("Something ///// tags")).toThrow();

  // No tags
  expect(() => getRawLowerAudioTags("    //  ")).toThrow();
  expect(() => getRawLowerAudioTags("   something //  // something")).toThrow();
  expect(() => getRawLowerAudioTags("////")).toThrow();

  // No separators
  expect(getRawLowerAudioTags("   ")).toEqual([]);

  audioTestCases.forEach((line, i) => {
    expect(getRawLowerAudioTags(line)).toEqual(expectedAudioRawTags[i]);
  });
});

test("diary has only one-off", () => {
  const baseInput: NoteInput = {
    noteText: `@@DIARY indicator alone should not have an individual patch
This is @@d, everything one-off @@friends

para with @@p tag
another @@o@@b @@d    
normal tags @@u @@business
this is just diary`,
  };

  const universalTags: string[] = [];

  const expected: TaggedInput[] = [
    { localTags: ["d"], input: baseInput, universalTags: [] },
    {
      localTags: ["friends"],
      input: {
        noteText: "This is @@d, everything one-off @@friends",
      },
      universalTags,
    },
    {
      localTags: ["p"],
      input: { noteText: "para with @@p tag" },
      universalTags,
    },
    {
      localTags: ["b"],
      input: {
        noteText: "another @@o@@b @@d",
      },
      universalTags,
    },
    {
      localTags: ["business", "u"],
      input: {
        noteText: "normal tags @@u @@business", // no combining para tags for now
      },
      universalTags,
    },
  ];

  const result = getTaggedInputs(baseInput);

  expect(result.taggedInputs).toEqual(expected);

  // invalid tag in D
  expect(
    getTaggedInputs({ noteText: "@@d\n@@NotFoundTag" }).invalidTags,
  ).toEqual(["notfoundtag"]);

  // All with D not supported
  expect(() => getTaggedInputs({ noteText: "@@d\n@@b@@a" })).toThrow();
});

test("diary tag bug April 20", () => {
  const noteInput = {
    noteText:
      "@@d\r\n\r\nobv need to put v high pri on X reply... and on Sol eval.",
    date: "2023-04-19T03:30:12.000Z",
  };

  const inputs = getTaggedInputs(noteInput);
  const expected: TaggedInput = {
    localTags: ["d"],
    input: noteInput,
    universalTags: [],
  };
  expect(inputs.taggedInputs).toEqual([expected]);
});

test("just highlight with no tags", () => {
  expect(
    getTaggedInputs({ highlightText: "To a man with quick footwork..." }),
  ).toEqual({ invalidTags: [], taggedInputs: null });
});

test("Outlook with only 'o' tags", () => {
  const noteText = `
@@meximm @@o Imm water kinle passpt NEEDED, sun papers appt
 
@@ltragree @@o there is protection in affection... I believe
 
((2023-3-13))
`;

  const textLines = noteText.split("\n");

  const expectedInputs: TaggedInput[] = [
    {
      input: { noteText: textLines[1], date: "2023-03-13" },
      localTags: ["meximm"],
      universalTags: [],
    },
    {
      input: { noteText: textLines[3], date: "2023-03-13" },
      localTags: ["ltragree"],
      universalTags: [],
    },
  ];

  const result = getTaggedInputs({
    noteText,
  });

  expect(result.taggedInputs).toEqual(expectedInputs);
});

test("lone date line", () => {
  expect(isDateOnlyLine("test ((2023-3-13))")).toBeFalsy();
  expect(isDateOnlyLine("((2023-3-13))")).toBeTruthy();

  // Technically only dates, should handle elsewhere if multiple
  // dates are found
  expect(isDateOnlyLine("((2023-3-13)) ((2023-3-14))")).toBeTruthy();
});

test("multiple dates throw", () => {
  // Same format
  expect(() => getCustomDateFromText("((2023-3-13)) ((2023-3-14))")).toThrow();

  // Different formats
  expect(() => getCustomDateFromText("((2023-3-13)) ((3-14))")).toThrow();
});
