import { TagIdPairing } from "./tagIdPairings";
import { prepRawTags } from "./TagNotePairHelpers";
import { validateConfig } from "./validateConfig";

const TEST_ID = "1aaabbbbccccdddd1111222233334444";
const ID_2 = "2aaabbbbccccdddd1111222233334444";
const ID_3 = "3aaabbbbccccdddd1111222233334444";
const ID_4 = "4aaabbbbccccdddd1111222233334444";

test("all ok", () => {
  const tagPairs: TagIdPairing[] = [
    {
      tags: ["first", "second"],
      noteId: TEST_ID,
    },
    {
      tags: ["third", "fourth"],
      noteId: ID_2,
    },
    {
      tags: ["5", "6"],
      folderId: ID_3,
    },
    {
      tags: ["7", "8"],
      folderId: ID_4,
    },
  ];

  expect(validateConfig(tagPairs)).toBe(null);
});

test("section and page ID (throws)", () => {
  const tagPairs: TagIdPairing[] = [
    {
      tags: ["1", "2"],
      noteId: TEST_ID,
      folderId: TEST_ID,
    },
  ] as any;

  expect(validateConfig(tagPairs)).not.toBe(null);
});

test("joplin id", () => {
  const goodPairs: TagIdPairing[] = [
    {
      tags: ["1", "2"],
      noteId: "abcdefabcdefabcdef01234567890123",
    },
  ];

  expect(validateConfig(goodPairs)).toBeNull();

  const goodFolderPairs: TagIdPairing[] = [
    {
      tags: ["1", "2"],
      folderId: "abcdefabcdefabcdef01234567890123",
    },
  ];

  expect(validateConfig(goodPairs)).toBeNull();
  expect(validateConfig(goodFolderPairs)).toBeNull();

  const badIdData: TagIdPairing[] = [
    {
      tags: ["1", "2"],
      noteId: "bad-id",
    },
  ];

  expect(validateConfig(badIdData)).not.toBeNull();
});

test("duplicate ID (throws)", () => {
  const tagPairs: TagIdPairing[] = [
    {
      tags: ["1", "2"],
      noteId: TEST_ID,
    },
    {
      tags: ["3", "4"],
      noteId: TEST_ID,
    },
  ];

  expect(validateConfig(tagPairs)).not.toBe(null);
});

test("comma in tag - common typo", () => {
  const tagPairs: TagIdPairing[] = [
    {
      tags: ["commainnote,"],
      noteId: TEST_ID,
    },
  ];

  expect(validateConfig(tagPairs)).not.toBe(null);
});

test("duplicate tag - diff item (throws)", () => {
  const tagPairs: TagIdPairing[] = [
    {
      tags: ["1"],
      noteId: TEST_ID,
    },
    {
      tags: ["1"],
      noteId: ID_2,
    },
  ];

  expect(validateConfig(tagPairs)).not.toBe(null);
});

test("no section OR page id throws", () => {
  const tagPairs: TagIdPairing[] = [{ tags: ["1"] }] as any;

  expect(validateConfig(tagPairs)).not.toBe(null);
});

test("blank string section throws", () => {
  const tagPairs: TagIdPairing[] = [{ tags: ["1"], folderId: "" }];

  expect(validateConfig(tagPairs)).not.toBe(null);
});

test("blank string page throws", () => {
  const tagPairs: TagIdPairing[] = [{ tags: ["1"], noteId: "" }];

  expect(validateConfig(tagPairs)).not.toBe(null);
});

test("blank tag throws", () => {
  const tagPairs: TagIdPairing[] = [{ tags: ["", "test"], noteId: TEST_ID }];

  expect(validateConfig(tagPairs)).not.toBe(null);
});

test("bad page ID throws", () => {
  const tagPairs: TagIdPairing[] = [
    { tags: ["something", "test"], noteId: "tooshort" },
  ];

  expect(validateConfig(tagPairs)).not.toBe(null);
});

test("bad section ID throws", () => {
  const tagPairs: TagIdPairing[] = [
    {
      tags: ["something", "test"],
      folderId:
        "toolongggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggggg",
    },
  ];

  expect(validateConfig(tagPairs)).not.toBe(null);

  const tagPairs2: TagIdPairing[] = [
    {
      tags: ["something", "test"],
      folderId: "invalid-chars!",
    },
  ];

  expect(validateConfig(tagPairs2)).not.toBe(null);
});

test("prepTags", () => {
  const rawTags = ["a", "B", "a", "C"];
  const tags = prepRawTags(rawTags);
  expect(tags).toEqual(["a", "b", "c"]);
});

test("bad chars in tag", () => {
  // Some bad chars regex might miss: https://stackoverflow.com/a/26900132/152711
  const badChars = "[]\"'\\^ ×÷";
  for (let i = 0; i < badChars.length; ++i) {
    expect(
      validateConfig([{ tags: [`${badChars[i]}`], noteId: TEST_ID }]),
    ).not.toBeNull();
  }

  expect(
    validateConfig([
      { tags: ['fempreference","womanpreference'], noteId: TEST_ID },
    ]),
  ).not.toBeNull();
});

test("duplicate tags in SAME entry ok", () => {
  const tagPairs: TagIdPairing[] = [
    {
      tags: ["duplicate", "duplicate"],
      noteId: TEST_ID,
    },
  ];

  expect(validateConfig(tagPairs)).toBeNull();
});
