import { TAG_ID_PAIRINGS } from "./PairingHelpers";
import { TagIdPairing } from "./tagIdPairings";
import { Assert, hasValue, hv } from "./Tools";

// Works for folder or page
export const joplinIdRegex = /[a-f0-9]{32}$/;

// Called after prepRawTags
export function validateConfig(
  validationPairs: TagIdPairing[] = TAG_ID_PAIRINGS,
): string[] | null {
  const tagSet = new Set<string>();
  const idSet = new Set<string>();
  const errorMessages: string[] = [];

  validationPairs.forEach(
    ({ tags, noteId: joplinPageId, folderId: joplinFolderId }) => {
      try {
        if (hv(joplinFolderId)) {
          Assert.hard(!hasValue(joplinPageId));
          Assert.hard(
            joplinIdRegex.test(joplinFolderId),
            `invalid J id: ${joplinFolderId}`,
          );
          if (idSet.has(joplinFolderId)) {
            throw new Error(`Duplicate J folder ID: ${joplinPageId}`);
          }
          idSet.add(joplinFolderId);
        } else if (hv(joplinPageId)) {
          if (!joplinIdRegex.test(joplinPageId)) {
            throw new Error("invalid Joplin page ID format");
          }
          if (idSet.has(joplinPageId)) {
            throw new Error(`Duplicate J page ID: ${joplinPageId}`);
          }
          idSet.add(joplinPageId);
        } else {
          throw new Error(`No Joplin ID: ${tags.toString()}`);
        }

        tags.forEach(t => {
          // accented letters ok:
          // https://stackoverflow.com/a/26900132/152711
          Assert.hard(
            !/[^A-Za-zÀ-ÖØ-öø-ÿ0-9_]/.test(t),
            `Non-word characters found in tag: "${t}"`,
          ); // only word characters
          if (tagSet.has(t)) {
            throw new Error(`Duplicate tag entry: ${t}`);
          }

          if (t.trim() === "") {
            throw new Error(`Empty tag`);
          }

          if (t.trim() !== t) {
            throw new Error(`Not trimmed: ${t}`);
          }
        });

        tags.forEach(t => tagSet.add(t));
      } catch (e) {
        errorMessages.push((e as Error).message);
      }
    },
  );

  return errorMessages.length > 0 ? errorMessages : null;
}
