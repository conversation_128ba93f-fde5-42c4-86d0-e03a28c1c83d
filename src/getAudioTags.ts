const audioNoteSeparators = [
  "//",
  "slash/",
  "/lash",
  "/slash",
  "/flash",
  "slash slash", // Echo has misheard me
  "flash flash", // <PERSON> has misheard me
  "/last",
];

const commaSplitRegex = / comma |,| come a /;

// "@@" I'll sometimes add if manually tagging audio
// "." is in e.g. J.K. Rowling
// For some reason <PERSON><PERSON> likes writing: !(CDMX)
// And apostrophe for possessives I think
// Dash was in "personal non-fiction 7"
const audioCharsToRemoveRegex = /[!()'@.-]/g;

export function getRawLowerAudioTags(rawLine: string): string[] {
  const line = rawLine.toLowerCase(); // simplify for getting tags

  const sepFoundList: {
    iStart: number;
    length: number;
  }[] = [];

  if (/(\/){5}/.test(line)) {
    throw new Error(`Unexpected five-plus slashes found: ${line}`);
  }

  if (/(?<!\/)\/{3}(?!\/)/.test(line)) {
    throw new Error(`Unexpected three slashes found: ${line}`);
  }

  for (let iChar = 0; iChar < line.length; ++iChar) {
    audioNoteSeparators.forEach((s, i) => {
      if (line.slice(iChar, iChar + s.length) === s) {
        if (i === 0 && iChar > 0 && line[iChar - 1] === ":") {
          // Exception for URL, ://
          return;
        }
        // Separator found
        sepFoundList.push({ iStart: iChar, length: s.length });
        iChar += s.length - 1;
      }
    });
  }

  const tagStrings: string[] = [];

  for (let iSep = 0; iSep < sepFoundList.length; ++iSep) {
    if (iSep % 2 === 1) {
      continue; // Closing separator; do not get proceeding text
    }

    const sep = sepFoundList[iSep];

    const iEnd =
      iSep < sepFoundList.length - 1
        ? sepFoundList[iSep + 1].iStart
        : line.length; // If no more separators, get all text to end of line

    tagStrings.push(line.slice(sep.iStart + sep.length, iEnd));
  }

  const rawTags = tagStrings
    // First split with spaces to capture " comma "
    .flatMap(str =>
      str.replaceAll(audioCharsToRemoveRegex, "").split(commaSplitRegex),
    )
    // Then remove all spaces since audio notes will have spaces for multi-word
    // tags
    .map(t => t.replaceAll(" ", ""));

  if (rawTags.includes("")) {
    throw new Error(`Audio slashes with no tags: ${line}`);
  }

  return rawTags;
}
