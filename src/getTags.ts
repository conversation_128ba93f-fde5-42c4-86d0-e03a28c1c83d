import * as DateFns from "date-fns";
import { TaggedInput } from "./Components/getPatchForTaggedInput";
import { getRawLowerAudioTags } from "./getAudioTags";
import { NoteInput } from "./NoteInput";
import {
  hasDiaryIndicator,
  includesUniversalTag,
  isDiaryIndicator,
  isOneOffIndicator,
  isUniversalIndicator,
  isFoundTag,
} from "./TagNotePairHelpers";
import { Assert, dedup, hasValueFn, hv } from "./Tools";

// This is the only function that gets a tag list, and it ensures that all
// tags are valid!  If they are not all valid, or not everything is tagged,
// it will NOT return any tagged inputs but rather an errorResult.
//
// This is probably the most critical function in the Autoparser.
//
// Never returns one-off or universal tags ("a", "o"...)
//
// Function MAY return untagged input, b/c a Universal patch may apply
export function getTaggedInputs(
  rawInput: NoteInput,
  {
    shouldAllowUniversal = false,
    shouldFindAudioTags = false,
    shouldAllowUntaggedPatches = false,
  } = {},
):
  | {
      taggedInputs: TaggedInput[];
      invalidTags: null;
    }
  | { taggedInputs: null; invalidTags: string[] } {
  const customDate = getCustomDateFromText(rawInput.noteText ?? "");
  const customDateString: string | undefined =
    hasValueFn(customDate, d => DateFns.format(d, "yyyy-MM-dd")) ?? undefined;
  const input: NoteInput = {
    date: customDateString ?? rawInput.date,
    highlightText:
      hasValueFn(rawInput.highlightText, t => stripCustomDates(t)) ?? undefined,
    noteText:
      hasValueFn(rawInput.noteText, t => stripCustomDates(t)) ?? undefined,
    sourceTitle: rawInput.sourceTitle,
  };

  const allNoteLines = input.noteText
    ?.trim()
    .split("\n")
    .map(line => line.trim())
    .filter(l => l.length > 0);
  const isDiaryInput = hasDiaryIndicator(input.noteText ?? "");

  const {
    oneOffLines,
    localNoteTags: noteTags,
    universalTags,
    invalidTags,
  } = (allNoteLines ?? []).reduce(
    (acc, line) => {
      const rawTextTags = getSortedRawLowerTagsForText(line, {
        shouldAllowUniversalWithOneOff: true,
      });
      const rawAudioTags = shouldFindAudioTags
        ? getRawLowerAudioTags(line)
        : [];
      const rawTags = dedup([...rawTextTags, ...rawAudioTags].toSorted());
      const isOneOff = isDiaryInput || rawTags.some(t => isOneOffIndicator(t));
      const hasUniversal = rawTags.some(t => isUniversalIndicator(t));

      Assert.hard(
        !hasUniversal || shouldAllowUniversal,
        `Prohibited All tag found for input: ${rawInput.highlightText} ${rawInput.noteText}`,
      );

      const newUniversalTags = hasUniversal
        ? rawTags.filter(t => !isUniversalIndicator(t))
        : [];

      // One-off and Universal together are not supported
      Assert.hard(
        !isOneOff || newUniversalTags.length === 0,
        `One-off found with Universal: ${rawInput.highlightText} ${rawInput.noteText}`,
      );

      const newNotFoundTags = rawTags.filter(t => !isFoundTag(t));

      const newSharedTags = {
        universalTags: [...acc.universalTags, ...newUniversalTags],
        invalidTags: [...acc.invalidTags, ...newNotFoundTags],
      };

      const shouldSkip = isOneOff && rawTags.every(t => isDiaryIndicator(t));

      if (shouldSkip) {
        // we will add this content with the overall Diary patch below
        return acc;
      } else if (isOneOff) {
        return {
          ...acc,
          ...newSharedTags,
          oneOffLines: [...acc.oneOffLines, line],
        };
      } else {
        const newLocalTags: string[] = rawTags.filter(
          t => !isUniversalIndicator(t) && !newUniversalTags.includes(t),
        );
        return {
          ...acc,
          ...newSharedTags,
          localNoteTags: [...acc.localNoteTags, ...newLocalTags],
        };
      }
    },
    {
      localNoteTags: [] as string[],
      oneOffLines: [] as string[],
      universalTags: [] as string[],
      invalidTags: [] as string[],
    },
  );

  if (universalTags.length > 0 && isDiaryInput) {
    throw new Error(`Universal indicators not supported in Diary`);
  }

  // Sometimes when directly editing I add tags to highlights,
  // much easier than pasting in a full Written Note block each time.
  const highlightTags = getSortedRawLowerTagsForText(input.highlightText ?? "");

  Assert.hard(
    !highlightTags.some(t => isOneOffIndicator(t)) &&
      !highlightTags.some(t => isUniversalIndicator(t)) &&
      !highlightTags.some(t => isDiaryIndicator(t)),
    "one-off, diary, universal tags not supported in Highlights",
  );

  const localTags = [...noteTags, ...highlightTags].toSorted();

  const hasNoTags =
    localTags.length === 0 &&
    oneOffLines.length === 0 &&
    !shouldAllowUniversal &&
    !isDiaryInput;

  if (hasNoTags) {
    // Error in this case
    console.log(`No tags for input:`);
    console.log(rawInput);

    if (shouldAllowUntaggedPatches) {
      // Just return success
      return { invalidTags: null, taggedInputs: [] };
    }
  }

  const hasGeneralTags = localTags.length > 0 || universalTags.length > 0;
  const areAllLinesOneOff =
    isDiaryInput ||
    (allNoteLines ?? []).filter(l => !isDateOnlyLine(l)).length ===
      oneOffLines.length;

  const wrappedDiaryInputArr: TaggedInput[] = isDiaryInput
    ? [
        {
          input,
          localTags: ["d"],
          universalTags: [],
        },
      ]
    : [];

  // If there are no general tags AND no untagged non-empty general lines, and we DO
  // have a one off result, then it's *all* one-off.  No general result.
  const wrappedGeneralInputArr: TaggedInput[] =
    !hasGeneralTags && areAllLinesOneOff
      ? []
      : [
          {
            input,
            localTags,
            universalTags,
          },
        ];
  const allTaggedInputs: TaggedInput[] = [
    ...wrappedGeneralInputArr,
    ...wrappedDiaryInputArr,
    ...oneOffLines.map(line => getTaggedInputForOneOffLine(line, input)),
  ];

  if (invalidTags.length > 0 || hasNoTags) {
    return { taggedInputs: null, invalidTags };
  }

  // if no general tags then universal must be possible (so it will be tagged
  // later), OR all lines have to be one-off.
  if (
    !hasGeneralTags &&
    !(shouldAllowUniversal || areAllLinesOneOff) &&
    !shouldAllowUntaggedPatches
  ) {
    return { taggedInputs: null, invalidTags: [] };
  }

  return { taggedInputs: allTaggedInputs, invalidTags: null };
}

export const dateOverrideRegexes: {
  regex: RegExp;
  paddedRegex: RegExp;
  dateFormatStr: string;
}[] = [
  {
    regex: /\(\(\d\d\d\d-\d\d?-\d\d?\)\)/g,
    paddedRegex: / \(\(\d\d\d\d-\d\d?-\d\d?\)\) /g,
    dateFormatStr: "yyyy-M-d",
  },
  {
    regex: /\(\(\d\d?-\d\d?\)\)/g,
    paddedRegex: / \(\(\d\d?-\d\d?\)\) /g,
    dateFormatStr: "M-d",
  },
  {
    regex: /\(\(\d\d\d\d\.\d\d?\.\d\d?\)\)/g,
    paddedRegex: / \(\(\d\d\d\d\.\d\d?\.\d\d?\)\) /g,
    dateFormatStr: "yyyy.M.d",
  },
  {
    regex: /\(\(\d\d?\.\w\w\w.\d\d\)\)/g,
    paddedRegex: / \(\(\d\d?\.\w\w\w.\d\d\)\) /g,
    dateFormatStr: "d.MMM.yy",
  },
  {
    regex: /\(\(\d\d?\.\d\d?\)\)/g,
    paddedRegex: / \(\(\d\d?\.\d\d?\)\) /g,
    dateFormatStr: "M.d",
  },
  {
    regex: /\(\(\d\d?-\d\d?-\d\d\d\d\)\)/g,
    paddedRegex: / \(\(\d\d?-\d\d?-\d\d\d\d\)\) /g,
    dateFormatStr: "M-d-yyyy",
  },
  {
    regex: /\(\(\d\d?-\d\d?-\d\d\)\)/g,
    paddedRegex: / \(\(\d\d?-\d\d?-\d\d\)\) /g,
    dateFormatStr: "M-d-yy",
  },
];

export function getCustomDateFromText(
  text: string,
  referenceDate = new Date(),
): null | Date {
  const matchDate = dateOverrideRegexes.reduce<Date | null>(
    (acc, { regex, dateFormatStr }) => {
      const match = text.match(regex);
      if (hv(acc) || !hv(match)) {
        Assert.hard(
          match === null || match.length === 0,
          `Multiple override dates found: ${text}`,
        );
        return acc;
      }

      Assert.hard(match.length === 1, `Multiple override dates found: ${text}`);

      const dateStr = match[0].replaceAll(/[\(\)]/g, ""); // eslint-disable-line no-useless-escape
      return DateFns.parse(dateStr, dateFormatStr, referenceDate);
    },
    null,
  );

  if (!hv(matchDate)) {
    Assert.hard(
      !/\(\(\d/.test(text),
      `Date-looking opening (( string found, text: ${text}`,
    );
    Assert.hard(
      !/\d\)\)/.test(text),
      `Date-looking closing )) string found, text: ${text}`,
    );
    return null;
  }

  const finalDate =
    matchDate > referenceDate ? DateFns.subYears(matchDate, 1) : matchDate;
  Assert.hard(finalDate <= referenceDate);

  // If date would be in the future, we assume it's last year
  return finalDate;
}
export function stripCustomDates(html: string) {
  return dateOverrideRegexes.reduce<string>(
    (str, { regex, paddedRegex }) =>
      // if surrounded by spaces, replace with just one space
      str.replace(paddedRegex, " ").replace(regex, ""),
    html,
  );
}

export function isDateOnlyLine(line: string) {
  return dateOverrideRegexes.some(
    r => line.replace(r.regex, "").trim().length === 0,
  );
}

function getTaggedInputForOneOffLine(line: string, noteInput: NoteInput) {
  return {
    input: { ...noteInput, noteText: line },
    localTags: getSortedRawLowerTagsForText(line).filter(
      t => !isOneOffIndicator(t) && !isDiaryIndicator(t),
    ),
    universalTags: [],
  };
}

// Returns all lowercase
export function getSortedRawLowerTagsForText(
  noteStr: string,
  { shouldAllowUniversalWithOneOff = false } = {},
): string[] {
  // "not preceding" syntax: https://stackoverflow.com/a/7317087/152711
  const disallowedNoteRegexps = [
    // Not including "@" for now since it's so common with e.g. Twitter mentions
    /(?<!([a-z\dá-úüñ]|(\.\.)))\.[mz][^.]/i, // partial prefix ".z"; digit is ok e.g. 1.Mar.2022; letter is ok for URLs like airbnb.me. triple dot ok e.g. "...me"
    /(?<![a-z\dá-úüñ./])[mz]\.[a-z\dá-úüñ]/i, // partial prefix "z.". Exception for URL "/m."
    /(?<![.a-z\dá-úüñ])\.\.[a-z\dá-úüñ]/i, // Possibly missed z or m between dots
    /(?<![ap])\.[mz]\.(?![a-z\dá-úüñ])/i, // A.M./P.M. ok. regex "lookarounds" https://stackoverflow.com/a/27879390/152711
    /@@(?![a-z\dá-úüñ])/i,
    /;;(?![a-z\dá-úüñ])/i,
    /^\;[a-z\dá-úüñ]+/i,
    /\s\;[a-z\dá-úüñ]+/i,
  ];
  disallowedNoteRegexps.forEach(r =>
    Assert.hard(
      !r.test(noteStr ?? ""),
      `Regexp matched: ${r}\nFor text: ${noteStr}`,
    ),
  );

  const atMatches = noteStr.match(/@@[a-z\dá-úüñ]+/gi) ?? [];
  const atTags =
    atMatches.map(match => match.replace("@@", "").toLowerCase()) ?? [];

  const scMatches = noteStr.match(/;;[a-z\dá-úüñ]+/gi) ?? [];
  const scTags =
    scMatches.map(match => match.replace(";;", "").toLowerCase()) ?? [];

  const bangMatches = noteStr.match(/!![a-z\dá-úüñ]+/gi) ?? [];
  const bangTags =
    bangMatches.map(match => match.replace("!!", "").toLowerCase()) ?? [];

  const zMatches = noteStr.match(/\.z\.[a-z\dá-úüñ]+/gi) ?? [];
  const zTags =
    zMatches.map(match => match.replace(/\.z\./i, "").toLowerCase()) ?? [];

  const mMatches = noteStr.match(/\.m\.[a-z\dá-úüñ]+/gi) ?? [];
  const mTags =
    mMatches.map(match => match.replace(/\.m\./i, "").toLowerCase()) ?? [];

  const tags = dedup([
    ...zTags,
    ...mTags,
    ...atTags,
    ...scTags,
    ...bangTags,
  ]).toSorted();

  Assert.hard(
    tags.length === 0 || tags.filter(t => !isOneOffIndicator(t)).length > 0,
    `One-off tag without matching specification tag: ${noteStr}`,
  );

  if (!shouldAllowUniversalWithOneOff) {
    const hasBoth =
      includesUniversalTag(tags) && tags.some(t => isOneOffIndicator(t));
    Assert.hard(
      !hasBoth,
      `Same input contains both all and one-off: ${noteStr}`,
    );
  }
  return tags;
}
