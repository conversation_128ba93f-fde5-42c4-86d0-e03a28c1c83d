import { J_UNFILED_FOLDER_ID } from "./Jo<PERSON><PERSON>/Constants";
import {
  fetchAllJoplinFolders,
  fetchLong<PERSON><PERSON>linList,
  moveNotesToFolder,
} from "./Jo<PERSON><PERSON>/Jo<PERSON>linShared";

export async function moveOrphanedNotesToUnfiled() {
  console.log("Moving orphaned...");
  const folderIdSet = new Set(
    (await fetchAllJoplinFolders<{ id: string }>({ fields: "id" })).map(
      f => f.id,
    ),
  );

  const allNotes = await fetchLongJoplinList<{ id: string; parent_id: string }>(
    "notes",
    {
      fields: "id,parent_id",
    },
  );

  const orphanedNotes = allNotes.filter(n => !folderIdSet.has(n.parent_id));

  console.log(`Orphaned count: ${orphanedNotes.length}`);

  await moveNotesToFolder(orphanedNotes, J_UNFILED_FOLDER_ID);
}
